
    # # 内存数据库访问接口
    # def get_market_data(self, instrument_id: str) -> Optional[MarketDataMsg]:
    #     """获取合约行情快照

    #     Args:
    #         instrument_id: 合约ID

    #     Returns:
    #         MarketDataMsg: 市场数据对象，如果不存在返回None
    #     """
    #     return self._engine.get_market_data(instrument_id)

    # def get_all_market_data(self) -> List[MarketDataMsg]:
    #     """获取所有合约行情快照

    #     Returns:
    #         List[MarketDataMsg]: 市场数据对象列表
    #     """
    #     return self._engine.get_all_market_data()

    # def get_future_instrument(self, instrument_id: str) -> Optional[FutureInstrumentMsg]:
    #     """获取期货合约信息

    #     Args:
    #         instrument_id: 合约ID

    #     Returns:
    #         FutureInstrumentMsg: 期货合约对象，如果不存在返回None
    #     """
    #     return self._engine.get_future_instrument(instrument_id)

    # def get_all_future_instruments(self) -> List[FutureInstrumentMsg]:
    #     """获取所有期货合约信息

    #     Returns:
    #         List[FutureInstrumentMsg]: 期货合约对象列表
    #     """
    #     return self._engine.get_all_future_instruments()

    # def get_option_instrument(self, instrument_id: str) -> Optional[OptionInstrumentMsg]:
    #     """获取期权合约信息

    #     Args:
    #         instrument_id: 合约ID

    #     Returns:
    #         OptionInstrumentMsg: 期权合约对象，如果不存在返回None
    #     """
    #     return self._engine.get_option_instrument(instrument_id)

    # def get_all_option_instruments(self) -> List[OptionInstrumentMsg]:
    #     """获取所有期权合约信息

    #     Returns:
    #         List[OptionInstrumentMsg]: 期权合约对象列表
    #     """
    #     return self._engine.get_all_option_instruments()

    # def get_position(self, instrument_id: str, direction: int) -> Optional[PositionMsg]:
    #     """获取持仓信息

    #     Args:
    #         instrument_id: 合约ID
    #         direction: 方向，0表示多头，1表示空头

    #     Returns:
    #         PositionMsg: 持仓对象，如果不存在返回None
    #     """
    #     return self._engine.get_position(instrument_id, direction)

    # def get_all_positions(self) -> List[PositionMsg]:
    #     """获取所有持仓信息

    #     Returns:
    #         List[PositionMsg]: 持仓对象列表
    #     """
    #     return self._engine.get_all_positions()

    # def get_order(self, order_id: int) -> Optional[OrderMsg]:
    #     """获取订单信息

    #     Args:
    #         order_id: 订单ID

    #     Returns:
    #         OrderMsg: 订单对象，如果不存在返回None
    #     """
    #     return self._engine.get_order(order_id)

    # def get_all_orders(self) -> List[OrderMsg]:
    #     """获取所有订单信息

    #     Returns:
    #         List[OrderMsg]: 订单对象列表
    #     """
    #     return self._engine.get_all_orders()

    # def get_trade(self, trade_id: int) -> Optional[TradeMsg]:
    #     """获取成交信息

    #     Args:
    #         trade_id: 成交ID

    #     Returns:
    #         TradeMsg: 成交对象，如果不存在返回None
    #     """
    #     return self._engine.get_trade(trade_id)

    # def get_all_trades(self) -> List[TradeMsg]:
    #     """获取所有成交信息

    #     Returns:
    #         List[TradeMsg]: 成交对象列表
    #     """
    #     return self._engine.get_all_trades()



    # def get_market_data(self, instrument_id: str) -> Optional[MarketDataMsg]:
    #     """获取合约行情快照"""
    #     return self.mdb.get_record(table_enum.market_data, instrument_id)

    # def get_all_market_data(self) -> List[MarketDataMsg]:
    #     """获取所有合约行情快照"""
    #     return self.mdb.get_all_records(table_enum.market_data)

    # def get_future_instrument(self, instrument_id: str) -> Optional[FutureInstrumentMsg]:
    #     """获取期货合约信息"""
    #     return self.mdb.get_record(table_enum.future_instrument, instrument_id)

    # def get_all_future_instruments(self) -> List[FutureInstrumentMsg]:
    #     """获取所有期货合约信息"""
    #     return self.mdb.get_all_records(table_enum.future_instrument)

    # def get_option_instrument(self, instrument_id: str) -> Optional[OptionInstrumentMsg]:
    #     """获取期权合约信息"""
    #     return self.mdb.get_record(table_enum.option_instrument, instrument_id)

    # def get_all_option_instruments(self) -> List[OptionInstrumentMsg]:
    #     """获取所有期权合约信息"""
    #     return self.mdb.get_all_records(table_enum.option_instrument)

    # def get_position(self, instrument_id: str, direction: int) -> Optional[PositionMsg]:
    #     """获取持仓信息"""
    #     key = f"{instrument_id}_{direction}"
    #     return self.mdb.get_record(table_enum.position, key)

    # def get_all_positions(self) -> List[PositionMsg]:
    #     """获取所有持仓信息"""
    #     return self.mdb.get_all_records(table_enum.position)

    # def get_order(self, order_id: int) -> Optional[OrderMsg]:
    #     """获取订单信息"""
    #     return self.mdb.get_record(table_enum.order, str(order_id))

    # def get_all_orders(self) -> List[OrderMsg]:
    #     """获取所有订单信息"""
    #     return self.mdb.get_all_records(table_enum.order)

    # def get_trade(self, trade_id: int) -> Optional[TradeMsg]:
    #     """获取成交信息"""
    #     return self.mdb.get_record(table_enum.trade, str(trade_id))

    # def get_all_trades(self) -> List[TradeMsg]:
    #     """获取所有成交信息"""
    #     return self.mdb.get_all_records(table_enum.trade)