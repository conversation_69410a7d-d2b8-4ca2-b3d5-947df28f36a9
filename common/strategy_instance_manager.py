from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List
from threading import Lock
from py_strategy_api.strategy_api import Strategy<PERSON>pi
from utils.flog import flogger
from py_strategy_api.fb_enum import StrategyStateEnum
from common.models import *
from common.enum import *
from common.mdb import MemoryDatabase
from common.table_enum import *

@dataclass
class StrategyInstanceParameter:
    """策略参数"""
    strategy_instance_id: int
    param_name: str
    param_type: str
    param_value: Any
    node_id: int = 1

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class StrategyInstanceWrapper:
    def __init__(self, strategy_instance_id: int, strategy_api: StrategyApi, node_id: int):
        self.strategy_instance_id: int = strategy_instance_id
        self.strategy_api: StrategyApi = strategy_api
        self.node_id: int = node_id
        self.trading_account_id: int = None
        self.status: str = None
        self.strategy_name: str = None
        self.strategy_instance_name: str = None
        # 设置策略实例中的id
        self.set_strategy_instance_id(strategy_instance_id)
        # 缓存策略参数
        self.parameters: Dict[str, Any] = {}
        self._update_parameters_in_wrapper()

    def get_strategy_instance_api(self) -> StrategyApi:
        return self.strategy_api

    def get_trading_account_id(self) -> int:
        return self.trading_account_id

    def set_trading_account_id(self, trading_account_id: int) -> None:
        self.trading_account_id = trading_account_id

    def get_strategy_instance_id(self) -> int:
        return self.strategy_instance_id

    def set_strategy_instance_id(self, strategy_instance_id: int) -> None:
        self.strategy_api._strategy_id = strategy_instance_id

    def get_strategy_name(self) -> str:
        return self.strategy_name

    def set_strategy_name(self, strategy_name: str) -> None:
        self.strategy_name = strategy_name

    def get_strategy_instance_name(self) -> str:
        return self.strategy_instance_name

    def set_strategy_instance_name(self, strategy_instance_name: str) -> None:
        self.strategy_instance_name = strategy_instance_name

    def get_strategy_instance_status(self) -> str:
        return self.status

    def set_strategy_instance_status(self, status: str) -> None:
        self.status = status

    def _update_parameters_in_wrapper(self) -> None:
        if hasattr(self.strategy_api, 'parameters') and self.strategy_api.parameters:
            for name in self.strategy_api.parameters:
                if hasattr(self.strategy_api, name):
                    value = getattr(self.strategy_api, name)
                    self.parameters[name] = value

    def get_parameters(self) -> Dict[str, Any]:
        """获取完整的参数对象"""
        return self.parameters

    def update_parameter(self, param_name: str, param_value: Any) -> bool:
        """更新策略参数"""
        if hasattr(self.strategy_api, param_name):
            # 更新策略API实例中的参数
            setattr(self.strategy_api, param_name, param_value)
            self._update_parameters_in_wrapper()
            return True
        else:
            flogger.error("parameter not exist in strategy instance",
                          strategy_instance_id=self.strategy_instance_id, param_name=param_name, param_value=param_value)
            return False

    def get_parameter_names(self) -> List[str]:
        """获取所有参数名称"""
        return list(self.parameters.keys())

    def has_parameter(self, param_name: str) -> bool:
        """检查是否有指定参数"""
        return param_name in self.parameters

    def get_parameter_type(self, param_name: str) -> Optional[str]:
        """获取参数类型"""
        param = self.parameters.get(param_name)
        return type(param).__name__ if param else None


class StrategyInstanceManager:
    """策略实例管理器 - 单例模式，管理所有策略实例和信息"""

    _instance = None
    _lock = Lock()

    def __init__(self, node_id, mdb: MemoryDatabase):
        self.node_id: int = node_id
        self._instance_lock = Lock()
        self.mdb = mdb
        # 策略包装器字典 - 存储实际的策略实例
        self.strategy_instance_wrappers: Dict[int,
                                              StrategyInstanceWrapper] = {}
        flogger.info("strategy instance manager initialized", node_id=node_id)

    @classmethod
    def get_instance(cls, node_id: int, mdb: MemoryDatabase) -> 'StrategyInstanceManager':
        """获取单例实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = cls(node_id, mdb)
            return cls._instance

    def _update_strategy_instance_mdb(self, strategy_instance_id: int) -> None:
        instance_wrapper: StrategyInstanceWrapper = self.strategy_instance_wrappers.get(
            strategy_instance_id)
        if not instance_wrapper:
            flogger.error("strategy instance wrapper not found",
                          strategy_instance_id=strategy_instance_id)
            return
        try:
            strategy_instance_msg = StrategyInstanceMsg(
                strategy_instance_id=strategy_instance_id,
                strategy_name=self.get_strategy_name(strategy_instance_id),
                strategy_engine_id=self.node_id,
                strategy_instance_name=self.get_strategy_instance_name(
                    strategy_instance_id),
                user_id=0,
                strategy_instance_state=self.get_strategy_instance_status(
                    strategy_instance_id),
                trading_account_id=self.get_strategy_instance_trading_account_id(
                    strategy_instance_id),
                strategy_instance_priority=0,
                last_operator_id=0,
                strategy_pause_reason=StrategyPauseReasonEnum.PAUSE_BY_USER_OPERATOR.value
            )
            flogger.info("update strategy instance mdb",
                         strategy_instance_id=strategy_instance_id, data=strategy_instance_msg)
            self.mdb.update_record(
                table_enum.strategy_instance, strategy_instance_msg)
        except Exception as e:
            flogger.error("update mdb record failed", table=table_enum.strategy_instance,
                          strategy_instance_id=strategy_instance_id, data=strategy_instance_msg, error=str(e))

    def add_strategy_instance_wrapper(self, strategy_instance_id: int, strategy_name: str, strategy_instance_name: str, trading_account_id: int, strategy_api: StrategyApi) -> bool:
        """添加策略包装器"""
        with self._instance_lock:
            try:
                # 创建策略包装器
                wrapper = StrategyInstanceWrapper(
                    strategy_instance_id=strategy_instance_id,
                    strategy_api=strategy_api,
                    node_id=self.node_id
                )
                wrapper.set_trading_account_id(trading_account_id)
                wrapper.set_strategy_name(strategy_name)
                wrapper.set_strategy_instance_name(strategy_instance_name)
                wrapper.set_strategy_instance_status(
                    StrategyStateEnum.STRATEGY_INIT_STAT.value)

                # 存储策略包装器
                self.strategy_instance_wrappers[strategy_instance_id] = wrapper

                self._update_strategy_instance_mdb(strategy_instance_id)

                flogger.info("strategy instance wrapper added",
                             strategy_instance_id=strategy_instance_id,
                             strategy_name=strategy_name,
                             strategy_instance_name=strategy_instance_name,
                             trading_account_id=wrapper.get_trading_account_id(),
                             status=StrategyStateEnum.STRATEGY_INIT_STAT.value,
                             node_id=self.node_id)
                return True

            except Exception as e:
                flogger.error("failed to add strategy wrapper",
                              strategy_instance_id=strategy_instance_id, error=str(e))
                return False

    def check_strategy_instance_exists(self, strategy_instance_id) -> bool:
        """检查策略实例是否存在"""
        return strategy_instance_id in self.strategy_instance_wrappers

    def get_strategy_instance_name(self, strategy_instance_id: int) -> Optional[str]:
        """获取策略实例名称"""
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_strategy_instance_name()

    def get_strategy_name(self, strategy_instance_id: int) -> Optional[str]:
        """获取策略名称"""
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_strategy_name()

    def get_strategy_instance_id_list(self) -> List[int]:
        """获取策略实例id列表"""
        return list(self.strategy_instance_wrappers.keys())

    def get_strategy_instance_wrapper(self, strategy_instance_id: int) -> Optional[StrategyInstanceWrapper]:
        """获取策略包装器"""
        return self.strategy_instance_wrappers.get(strategy_instance_id)

    def remove_strategy_instance_wrapper(self, strategy_instance_id: int) -> bool:
        """移除策略包装器"""
        with self._instance_lock:
            wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
            if not wrapper:
                flogger.warning("strategy instance wrapper not found",
                                strategy_instance_id=strategy_instance_id)
                return False
            try:
                wrapper.set_strategy_instance_status(
                    StrategyStateEnum.STRATEGY_DELETE_STAT.value)
                self._update_strategy_instance_mdb(strategy_instance_id)
                del self.strategy_instance_wrappers[strategy_instance_id]
                flogger.info("strategy instance wrapper removed",
                             strategy_instance_id=strategy_instance_id)
                return True
            except Exception as e:
                flogger.error("failed to remove strategy instance wrapper",
                              strategy_instance_id=strategy_instance_id, error=str(e))
                return False

    def get_strategy_instance_api(self, strategy_instance_id: int) -> Optional[StrategyApi]:
        """获取策略实例API"""
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_strategy_instance_api()

    def get_strategy_instance_trading_account_id(self, strategy_instance_id: int) -> Optional[int]:
        """获取策略的交易账户ID"""
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_trading_account_id()

    def get_strategy_instance_status(self, strategy_instance_id: int) -> str:
        """获取策略实例状态"""
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_strategy_instance_status()

    # ========== 策略信息管理功能 ==========

    def update_strategy_status(self, strategy_instance_id: int, status: str) -> bool:
        """更新策略状态"""
        flogger.info("update strategy instance status",
                     strategy_instance_id=strategy_instance_id, status=status)
        with self._instance_lock:
            wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
            if not wrapper:
                flogger.warning("strategy instance wrapper not found",
                                strategy_instance_id=strategy_instance_id)
                return False
            try:
                wrapper.set_strategy_instance_status(status)
                self._update_strategy_instance_mdb(strategy_instance_id)
            except Exception as e:
                flogger.error("update strategy status failed",
                              strategy_instance_id=strategy_instance_id, status=status, error=str(e))
            return True

    def update_strategy_parameter(self, strategy_instance_id: int, param_name: str, param_value: Any) -> bool:
        """更新策略参数"""
        with self._instance_lock:
            wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
            if wrapper:
                try:
                    # 是否暂停策略？
                    wrapper.update_parameter(param_name, param_value)
                    # self.strategy_instance_infos[strategy_instance_id].parameters = wrapper.get_parameters()
                except Exception as e:
                    flogger.warning("update parameter failed",
                                    strategy_instance_id=strategy_instance_id, param_name=param_name, error=str(e))
                    return False
            else:
                flogger.warning("parameter not found in strategy instance",
                                strategy_instance_id=strategy_instance_id, param_name=param_name)
                return False

            flogger.info("update strategy instance parameter", strategy_instance_id=strategy_instance_id,
                         param_name=param_name, param_value=param_value)
            return True

    def get_strategy_info(self, strategy_instance_id: int) -> Optional[StrategyInstanceMsg]:
        """获取策略信息"""
        flogger.info("get strategy instance info",
                     strategy_instance_id=strategy_instance_id)
        record = self.mdb.get_records_by_params(
            table_enum.strategy_instance, strategy_instance_id)
        return record

    def get_all_strategy_instance_infos(self) -> List[StrategyInstanceMsg]:
        """获取所有策略实例信息"""
        flogger.info("get all strategy instances info")
        records = self.mdb.get_all_records(table_enum.strategy_instance)
        return records

    def get_strategy_instance_parameters(self, strategy_instance_id: int) -> List[StrategyInstanceParameter]:
        """获取策略参数"""
        flogger.info("get strategy instance parameters",
                     strategy_instance_id=strategy_instance_id)
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return []
        parameters = []
        for name, value in wrapper.parameters.items():
            param = StrategyInstanceParameter(
                strategy_instance_id=strategy_instance_id,
                param_name=name,
                param_type=type(value).__name__,
                param_value=value
            )
            parameters.append(param)

        return parameters

    def delete_strategy_instance(self, strategy_id: int) -> bool:
        """删除策略实例"""
        return self.remove_strategy_instance_wrapper(strategy_id)

    # ========== 统计和监控功能 ==========

    def get_strategy_instance_count(self) -> int:
        """获取策略总数"""
        return len(self.strategy_instance_wrappers)

    def get_strategies_by_status(self, status: str) -> List[int]:
        """根据状态获取策略列表"""
        return [wrapper.get_strategy_instance_id() for wrapper in self.strategy_instance_wrappers.values() if wrapper.get_strategy_instance_status() == status]

    def get_strategies_by_account(self, trading_account_id: int) -> List[int]:
        """根据交易账户ID获取策略列表"""
        return [wrapper.get_strategy_instance_id() for wrapper in self.strategy_instance_wrappers.values()
                if wrapper.get_trading_account_id() == trading_account_id]
