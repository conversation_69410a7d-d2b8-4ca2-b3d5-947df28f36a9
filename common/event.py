from queue import Queue, Empty
from threading import Thread
from typing import Dict, List, Callable, Any
from dataclasses import dataclass
import time
from common.constants import *
from utils.flog import flogger

@dataclass
class Event:
    type: str
    data: Any = None

class EventEngine:
    """事件引擎，负责事件的分发和处理"""
    def __init__(self, interval: float = 0.001):
        flogger.info("event engine start init", interval = interval)
        self._queue = Queue()
        self._active = False
        self._thread = Thread(target=self._run, name="EventEngine")
        self._timer = Thread(target=self._run_timer, name="EventTimer")
        self._handlers: Dict[str, List[Callable]] = {}
        self._general_handlers: List[Callable] = []
        self._interval = interval
        flogger.info("event engine end init", interval = interval)

    def _run(self):
        """事件处理线程"""
        flogger.debug("event processing thread started")
        while self._active:
            try:
                event = self._queue.get(block=True, timeout=1)
                if event.type != EVENT_TIMER:
                    flogger.debug("event received", event_type = event.type)
                self._process(event)
            except Empty:
                pass
        flogger.debug("event processing thread stopped")

    def _process(self, event: Event):
        """处理事件"""
        if event.type in self._handlers:
            if event.type != EVENT_TIMER:
                flogger.debug("processing event", event_type = event.type)
            [handler(event) for handler in self._handlers[event.type]]

        if self._general_handlers:
            if event.type != EVENT_TIMER:
                flogger.debug("processing general event", event_type = event.type)
            [handler(event) for handler in self._general_handlers]

    def _run_timer(self):
        """定时器线程"""
        flogger.debug("time thread started", interval = self._interval)
        while self._active:
            time.sleep(self._interval)
            event = Event(type="timer")
            self.put(event)
        flogger.debug("time thread stopped")

    def start(self):
        """启动事件引擎"""
        flogger.debug("starting event engine", active_status = self._active)
        self._active = True
        self._thread.start()
        self._timer.start()
        flogger.debug("event engine started", active_status = self._active)

    def stop(self):
        """停止事件引擎"""
        flogger.debug("stopping evnet engine", active_status = self._active)
        self._active = False
        self._timer.join()
        self._thread.join()
        flogger.debug("event engine stopped", active_status = self._active)

    def put(self, event: Event):
        """放入事件"""
        if event.type != EVENT_TIMER:
            flogger.debug("put event", event_type = event.type)
        self._queue.put(event)

    def register(self, event_type: str, handler: Callable):
        """注册事件处理函数"""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
            flogger.debug("create event handler", event_type = event_type)

        if handler not in self._handlers[event_type]:
            self._handlers[event_type].append(handler)
            flogger.debug("register handler", event_type = event_type)

    def unregister(self, event_type: str, handler: Callable):
        """注销事件处理函数"""
        if event_type in self._handlers:
            if handler in self._handlers[event_type]:
                self._handlers[event_type].remove(handler)
                flogger.debug("unregister handler", event_type = event_type)

    def register_general(self, handler: Callable):
        """注册通用事件处理函数"""
        if handler not in self._general_handlers:
            self._general_handlers.append(handler)
            flogger.debug("register general handler")

    def unregister_general(self, handler: Callable):
        """注销通用事件处理函数"""
        if handler in self._general_handlers:
            self._general_handlers.remove(handler)
            flogger.debug("unregister general handler")
