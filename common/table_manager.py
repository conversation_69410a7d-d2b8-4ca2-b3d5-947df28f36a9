from functools import wraps
from common.mdb import MemoryDatabase
from common.models import *
from utils.flog import flogger
from common.table_enum import table_enum


def query_fields(*fields):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        wrapper.query_fields = list(fields)
        return wrapper
    return decorator


def init_tables(mdb: MemoryDatabase) -> None:

    flogger.info("init mdb tables")

    # 定义主键函数
    @query_fields('instrument_id')
    def market_data_pk(data: MarketDataMsg) -> str:  # 市场数据表主键
        return data.instrument_id

    @query_fields('user_name')
    def user_pk(data: UserMsg) -> str:  # 用户表主键
        return data.user_name

    @query_fields('order_id')
    def order_pk(data: OrderMsg) -> str:  # 订单表主键
        return str(data.order_id)

    @query_fields('trade_id')
    def trade_pk(data: TradeMsg) -> str:  # 成交表主键
        return str(data.trade_id)

    @query_fields('instrument_id')
    def security_instrument_pk(data: SecurityInstrumentMsg) -> str:  # 现货合约表主键
        return data.instrument_id

    @query_fields('instrument_id')
    def future_instrument_pk(data: FutureInstrumentMsg) -> str:  # 期货合约表主键
        return data.instrument_id

    @query_fields('instrument_id')
    def option_instrument_pk(data: OptionInstrumentMsg) -> str:  # 期权合约表主键
        return data.instrument_id

    @query_fields('strategy_instance_id')
    def strategy_instance_pk(data: StrategyInstanceMsg) -> str:  # 策略实例表主键
        return str(data.strategy_instance_id)

    @query_fields('instrument_id', 'trading_account_id')
    def position_pk(data: PositionMsg) -> str:  # 持仓表主键
        return f"{data.instrument_id}_{data.trading_account_id}"

    @query_fields('trading_account_id')
    def trading_account_pk(data: TradingAccountMsg) -> str:  # 交易账户表主键
        return str(data.trading_account_id)

    @query_fields('strategy_instance_id')
    def strategy_instance_pk(data: StrategyInstanceMsg) -> str:  # 策略实例表主键
        return str(data.strategy_instance_id)

    mdb.register_table(table_enum.market_data, market_data_pk)  # 市场数据表
    mdb.register_table(table_enum.user, user_pk)  # 用户表
    mdb.register_table(table_enum.order, order_pk)  # 订单表
    mdb.register_table(table_enum.trade, trade_pk)  # 成交表
    mdb.register_table(table_enum.security_instrument,
                       security_instrument_pk)  # 现货合约表
    mdb.register_table(table_enum.future_instrument,
                       future_instrument_pk)  # 期货合约表
    mdb.register_table(table_enum.option_instrument,
                       option_instrument_pk)  # 期权合约表
    mdb.register_table(table_enum.strategy_instance,
                       strategy_instance_pk)  # 策略实例表
    mdb.register_table(table_enum.position, position_pk)  # 持仓表
    mdb.register_table(table_enum.trading_account, trading_account_pk) # 交易账户表
    mdb.register_table(table_enum.strategy_instance, strategy_instance_pk) # 策略实例表

    flogger.info("finish init mdb tables")
