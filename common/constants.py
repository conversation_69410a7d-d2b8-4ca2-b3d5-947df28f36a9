from common.table_enum import table_enum

# 事件类型
EVENT_TIMER = "timer"
EVENT_MARKET_DATA = table_enum.market_data
EVENT_ORDER = table_enum.order
EVENT_TRADE = table_enum.trade

EVENT_USER = table_enum.user
EVENT_SECURITY_INSTRUMENT = table_enum.security_instrument
EVENT_FUTURE_INSTRUMENT = table_enum.future_instrument
EVENT_OPTION_INSTRUMENT = table_enum.option_instrument
EVENT_STRATEGY_INSTANCE = table_enum.strategy_instance
EVENT_POSITION = table_enum.position
EVENT_INSTRUMENT_PARAM_VALUE = table_enum.instrument_param_value
EVENT_CUSTOM_PARAM_VALUE = table_enum.custom_param_value
EVENT_TRADING_ACCOUNT = table_enum.trading_account

# 策略管理事件
EVENT_LOAD_STRATEGY = "load_strategy"
EVENT_CREATE_STRATEGY_INSTANCE = "create_strategy_instance"
EVENT_START_STRATEGY_INSTANCE = "start_strategy_instance"
EVENT_STOP_STRATEGY_INSTANCE = "stop_strategy_instance"
EVENT_DELETE_STRATEGY_INSTANCE = "delete_strategy_instance"
EVENT_QUERY_STRATEGIES = "query_strategies"
EVENT_UPDATE_STRATEGY_INSTANCE_PARAM = "update_strategy_instance_param"