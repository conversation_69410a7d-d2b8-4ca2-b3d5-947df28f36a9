# StrategyInstanceManager 使用指南

## 概述

`StrategyInstanceManager` 是一个单例模式的策略实例管理器，集成了策略包装器管理和策略信息管理的功能。它可以被 HTTP 服务和 Engine 服务共同使用，避免代码冗余。

## 主要功能

### 1. 单例模式
- 全局唯一实例，确保数据一致性
- 线程安全的操作
- 支持多个服务共享同一个管理器实例

### 2. 策略包装器管理
- 封装 StrategyApi 实例和交易账户ID
- 提供策略实例的完整生命周期管理
- 支持动态更新交易账户

### 3. 策略信息管理
- 维护策略的元信息（状态、参数等）
- 提供丰富的查询和统计功能
- 兼容原有的 StrategyManager 接口

## 核心类

### StrategyWrapper
```python
class StrategyWrapper:
    def __init__(self, strategy_id: int, strategy_api: StrategyApi, trading_account_id: Optional[int] = None)
    def get_strategy_api(self) -> StrategyApi
    def get_trading_account_id(self) -> Optional[int]
    def set_trading_account_id(self, trading_account_id: int) -> None
    def get_strategy_name(self) -> str
    def get_strategy_status(self) -> int
    def get_parameters(self) -> dict
```

### StrategyInfo
```python
@dataclass
class StrategyInfo:
    strategy_id: int
    strategy_name: str
    status: int
    parameters: Dict[str, Any]
    trading_account_id: Optional[int] = None
    node_id: int = 1
    created_time: Optional[str] = None
    last_updated_time: Optional[str] = None
```

## 使用方法

### 1. 获取管理器实例

```python
from common.strategy_instance_manager import StrategyInstanceManager

# 获取单例实例
manager = StrategyInstanceManager.get_instance(node_id=1)

# 或者直接创建（会返回同一个实例）
manager = StrategyInstanceManager(node_id=1)
```

### 2. 添加策略实例

```python
# 添加策略包装器（推荐方式）
success = manager.add_strategy_wrapper(
    strategy_id=12345,
    strategy_api=strategy_instance,
    trading_account_id=3
)

# 或者只添加策略信息（兼容原有接口）
manager.add_strategy(
    strategy_id=12345,
    strategy_name="DemoStrategy",
    status=1,
    parameters={"param1": 10, "param2": "test"},
    trading_account_id=3
)
```

### 3. 获取策略信息

```python
# 获取策略包装器
wrapper = manager.get_strategy_wrapper(strategy_id)
if wrapper:
    strategy_api = wrapper.get_strategy_api()
    account_id = wrapper.get_trading_account_id()
    print(f"策略: {wrapper.get_strategy_name()}, 账户: {account_id}")

# 获取策略信息
info = manager.get_strategy_info(strategy_id)
if info:
    print(f"策略状态: {info.status}")
    print(f"策略参数: {info.parameters}")

# 获取所有策略
all_wrappers = manager.get_all_strategy_wrappers()
all_infos = manager.get_all_strategy_infos()
```

### 4. 更新策略信息

```python
# 更新交易账户
success = manager.update_strategy_trading_account(strategy_id, new_account_id)

# 更新策略状态
success = manager.update_strategy_status(strategy_id, new_status)

# 更新策略参数
success = manager.update_strategy_parameter(strategy_id, "param_name", new_value)
```

### 5. 查询和统计

```python
# 根据状态查询策略
running_strategies = manager.get_strategies_by_status(status=2)  # 运行中的策略

# 根据交易账户查询策略
account_strategies = manager.get_strategies_by_account(trading_account_id=3)

# 获取统计摘要
summary = manager.get_summary()
print(f"总策略数: {summary['total_strategies']}")
print(f"状态分布: {summary['status_distribution']}")
print(f"账户分布: {summary['account_distribution']}")
```

### 6. 删除策略

```python
# 删除策略（会同时删除包装器和信息）
success = manager.delete_strategy(strategy_id)
```

## 在不同服务中的使用

### Engine 服务中使用

```python
class StrategyEngine:
    def __init__(self, ...):
        # 获取管理器实例
        self.strategy_manager = StrategyInstanceManager.get_instance(node_id=self.node_id)
    
    def _create_strategy_instance(self, strategy_name: str, params: Dict[str, Any]):
        strategy, strategy_id = self.strategy_factory.create_strategy(strategy_name, params)
        if strategy and strategy_id:
            # 添加到管理器
            trading_account_id = params.get('trading_account_id', 3)
            self.strategy_manager.add_strategy_wrapper(
                strategy_id=strategy_id,
                strategy_api=strategy,
                trading_account_id=trading_account_id
            )
        return strategy, strategy_id
    
    def send_order(self, strategy_id: int, order):
        # 从管理器获取交易账户ID
        account_id = self.strategy_manager.get_strategy_trading_account(strategy_id)
        return self.strategy_caller.send_order(strategy_id, order, account_id)
```

### HTTP 服务中使用

```python
class StrategyHttpHandler:
    def __init__(self, ...):
        # 获取同一个管理器实例
        self.strategy_manager = StrategyInstanceManager.get_instance()
    
    def get_all_strategies(self):
        # 获取所有策略信息
        strategies = self.strategy_manager.get_all_strategy_infos()
        return [strategy.to_dict() for strategy in strategies]
    
    def get_strategy_summary(self):
        # 获取统计摘要
        return self.strategy_manager.get_summary()
```

## 优势

1. **单例模式**: 确保全局数据一致性，避免重复创建
2. **线程安全**: 所有操作都有锁保护，支持多线程环境
3. **功能集成**: 将策略实例管理和信息管理集成在一起
4. **向后兼容**: 保持与原有 StrategyManager 接口的兼容性
5. **丰富查询**: 提供多种查询和统计功能
6. **代码复用**: HTTP 服务和 Engine 服务可以共享同一个管理器

## 注意事项

1. 由于是单例模式，第一次创建时的 `node_id` 会被保留
2. 所有操作都是线程安全的，但建议在高并发场景下合理设计调用频率
3. 删除策略时会同时清理包装器和信息，确保数据一致性
4. 建议在应用启动时就创建管理器实例，避免运行时的初始化开销
