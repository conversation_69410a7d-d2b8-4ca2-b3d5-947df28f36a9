from typing import Dict, Any, Callable
from common.event import EventEngine, Event
from common.mdb import MemoryDatabase
from common.strategy_instance_manager import StrategyInstanceManager
from py_strategy_api.fb_enum import StrategyStateEnum
from common.constants import *
from utils.flog import flogger


class StrategyStream:
    """策略数据流，负责处理来自 Front API 的消息，更新内存数据库，并分发给策略"""

    def __init__(self, event_engine: EventEngine, mdb: MemoryDatabase, strategy_manager: StrategyInstanceManager):
        flogger.info("strategy stream start init")
        self.event_engine = event_engine
        self.mdb = mdb
        self.strategy_manager = strategy_manager
        self._register_event_handlers()
        flogger.info("strategy stream end init")

    def _register_event_handlers(self):
        """注册事件处理函数"""
        # 需要进行策略回调的事件
        flogger.debug("registering callback events")
        self.event_engine.register(
            EVENT_MARKET_DATA, self._process_with_callback)
        self.event_engine.register(EVENT_ORDER, self._process_with_callback)
        self.event_engine.register(EVENT_TRADE, self._process_with_callback)
        self.event_engine.register(EVENT_POSITION, self._process_with_callback)
        self.event_engine.register(
            EVENT_INSTRUMENT_PARAM_VALUE, self._process_with_callback)
        self.event_engine.register(
            EVENT_CUSTOM_PARAM_VALUE, self._process_with_callback)
        self.event_engine.register(
            EVENT_SECURITY_INSTRUMENT, self._process_with_callback)
        self.event_engine.register(
            EVENT_FUTURE_INSTRUMENT, self._process_with_callback)
        self.event_engine.register(
            EVENT_OPTION_INSTRUMENT, self._process_with_callback)
        self.event_engine.register(
            EVENT_TRADING_ACCOUNT, self._process_with_callback)
        self.event_engine.register(
            EVENT_USER, self._process_without_callback)
        flogger.debug("event handlers registration completed")

    def _process_with_callback(self, event: Event):
        """处理需要进行策略回调的事件

        1. 更新内存数据库
        2. 分发给策略
        """
        # 获取事件类型和数据
        event_type = event.type
        flogger.debug("processing event with callback", event_type=event_type)
        data = event.data
        # 更新内存数据库
        flogger.debug("updating memory database", event_type=event_type)
        self.mdb.update_record(event_type, data)
        # 分发给策略
        flogger.debug("dispatching to strategies", event_type=event_type)
        self._dispatch_to_strategy_instances(event_type, data)

    def _process_without_callback(self, event: Event):
        """处理不需要进行策略回调的事件

        只更新内存数据库，不分发给策略
        """
        # 获取事件类型和数据
        event_type = event.type
        data = event.data
        flogger.debug("processing event without callback",
                      event_type=event_type)
        # 更新内存数据库
        flogger.debug("updating memory database", event_type=event_type)
        self.mdb.update_record(event_type, data)

    def _process_custom(self, event: Event, custom_handler: Callable[[Any], None]):
        """处理需要自定义处理的事件

        1. 更新内存数据库
        2. 调用自定义处理函数
        3. 分发给策略

        Args:
            event: 事件对象
            custom_handler: 自定义处理函数，接收事件数据作为参数
        """
        # 获取事件类型和数据
        event_type = event.type
        data = event.data

        # 更新内存数据库
        self.mdb.update_record(event_type, data)

        # 调用自定义处理函数
        custom_handler(data)

        # 分发给策略
        self._dispatch_to_strategy_instances(event_type, data)

    def _dispatch_to_strategy_instances(self, event_type: str, data: Any):
        """根据事件类型分发数据给策略

        Args:
            event_type: 事件类型，如 EVENT_MARKET_DATA
            data: 事件数据
        """
        flogger.debug("dispatching event to strategy instances",
                      event_type=event_type, strategy_count=self.strategy_manager.get_strategy_instance_count())
        # 获取策略方法
        method_func = None
        if event_type == EVENT_MARKET_DATA:
            def method_func(
                strategy_instance, data): return strategy_instance.on_market_data(data)
        elif event_type == EVENT_ORDER:
            def method_func(strategy_instance,
                            data): return strategy_instance.on_order(data)
        elif event_type == EVENT_TRADE:
            def method_func(strategy_instance,
                            data): return strategy_instance.on_trade(data)
        elif event_type == EVENT_USER:
            def method_func(strategy_instance,
                            data): return strategy_instance.on_user(data)
        elif event_type == EVENT_SECURITY_INSTRUMENT:
            def method_func(
                strategy_instance, data): return strategy_instance.on_security_instrument(data)
        elif event_type == EVENT_FUTURE_INSTRUMENT:
            def method_func(
                strategy_instance, data): return strategy_instance.on_future_instrument(data)
        elif event_type == EVENT_OPTION_INSTRUMENT:
            def method_func(
                strategy_instance, data): return strategy_instance.on_option_instrument(data)
        elif event_type == EVENT_POSITION:
            def method_func(strategy_instance,
                            data): return strategy_instance.on_position(data)
        elif event_type == EVENT_INSTRUMENT_PARAM_VALUE:
            def method_func(
                strategy_instance, data): return strategy_instance.on_instrument_param_value(data)
        elif event_type == EVENT_CUSTOM_PARAM_VALUE:
            def method_func(
                strategy_instance, data): return strategy_instance.on_custom_param_value(data)
        elif event_type == EVENT_TRADING_ACCOUNT:
            def method_func(
                strategy_instance, data): return strategy_instance.on_trading_account(data)
        else:
            flogger.warning("unknown event type", event_type=event_type)
            return

        strategy_instances_list = self.strategy_manager.get_strategy_instance_id_list()
        for strategy_instance_id in strategy_instances_list:
            status = self.strategy_manager.get_strategy_instance_status(
                strategy_instance_id)
            strategy_instance_api = self.strategy_manager.get_strategy_instance_api(
                strategy_instance_id)
            flogger.debug("checking strategy status",
                          strategy_instance_id=strategy_instance_id, status=status)
            if status == StrategyStateEnum.STRATEGY_RUNNING_STAT.value:
                flogger.debug("executing strategy callback",
                              strategy_instance_id=strategy_instance_id, event_type=event_type)
                method_func(strategy_instance_api, data)
