import json
import os
# import logging

from typing import Dict, Any, Optional, List, Union, get_type_hints
from dataclasses import dataclass, asdict, field

from pathlib import Path
from utils.logger import setup_logger, update_log_level

# 初始化全局日志器
logger = setup_logger('config')

# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.StreamHandler(),
#         logging.FileHandler('./log/py_fb_strategy.log')
#     ]
# )
# logger = logging.getLogger('py_fb_strategy')

@dataclass
class FrontApiConfig:
    """FRONT API 配置"""
    pub_address: str = "unix:////tmp/pengyc/MM_TOPIC_FRONT_SUMMARY_0_ALL"
    req_address: str = "unix:////tmp/pengyc/MM_TOPIC_FRONT_SERVICE_0_ALL"
    log_level: str = "info"
    log_dir: str = "./log/api.log"

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)    

@dataclass
class HttpServerConfig:
    """服务器配置"""
    host: str = "0.0.0.0"
    port: int = 5000

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class StrategyEngineConfig:
    """策略引擎配置"""
    node_id: int = 1

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class LogConfig:
    """日志配置"""
    level: str = "info"
    file: str = "./log/py_fb_strategy.log"

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class AppConfig:
    """应用程序配置"""
    front_api: FrontApiConfig = field(default_factory=FrontApiConfig)
    http_server: HttpServerConfig = field(default_factory=HttpServerConfig)
    strategy_engine: StrategyEngineConfig = field(default_factory=StrategyEngineConfig)
    log: LogConfig = field(default_factory=LogConfig)

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class Config:
    """配置管理类"""
    _instance = None

    @classmethod
    def get_instance(cls, config_file: str):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls(config_file)
        return cls._instance

    def __init__(self, config_file: str):
        # self.config_file = config_file
        #从main config.json传入绝对路径
        self.config_file = config_file
        self.config = AppConfig()
        self.load()

    def load(self) -> None:
        """加载配置文件"""
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r") as f:
                    config_dict = json.load(f)

                # 将字典转换为类实例
                self._update_config_from_dict(config_dict)
                logger.info(f"从 {self.config_file} 加载配置成功")
            except Exception as e:
                logger.error(f"从 {self.config_file} 加载配置失败: {e}")
                self.config = AppConfig()
                self.save()
        else:
            if self.config_file:
                logger.warning(f"配置文件 {self.config_file} 不存在，使用默认配置")
            else:
                logger.warning("未指定配置文件，使用默认配置")
            self.config = AppConfig()
            self.save()
        # 配置日志级别
        self._configure_logging()

    # def _configure_logging(self):
    #     """配置日志级别"""
    #     log_level_str = self.config.log.level.upper()
    #     log_level = getattr(logging, log_level_str, logging.INFO)

    #     # 设置日志级别
    #     logger.setLevel(log_level)

    #     # 确保日志目录存在
    #     log_dir = os.path.dirname(self.config.log.file)
    #     if not os.path.exists(log_dir):
    #         os.makedirs(log_dir)

    #     # 添加文件处理器
    #     for handler in logger.handlers:
    #         if isinstance(handler, logging.FileHandler):
    #             handler.baseFilename = self.config.log.file

    def _configure_logging(self):
        """配置日志级别"""
        print("++++++++++")
        log_level = self.config.log.level
        update_log_level(log_level)
        logger.info(f"setting log level {log_level}")

    def _update_config_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        # 更新 front_api 配置
        if "front_api" in config_dict:
            front_api_dict = config_dict["front_api"]
            self.config.front_api = FrontApiConfig(
                pub_address=front_api_dict.get("pub_address", self.config.front_api.pub_address),
                req_address=front_api_dict.get("req_address", self.config.front_api.req_address),
                log_level=front_api_dict.get("log_level", self.config.front_api.log_level),
                log_dir=front_api_dict.get("log_dir", self.config.front_api.log_dir)
            )

        # 更新 http_server 配置
        if "http_server" in config_dict:
            http_server_dict = config_dict["http_server"]
            self.config.http_server = HttpServerConfig(
                host=http_server_dict.get("host", self.config.http_server.host),
                port=http_server_dict.get("port", self.config.http_server.port)
            )

        # 更新 strategy_engine 配置
        if "strategy_engine" in config_dict:
            strategy_engine_dict = config_dict["strategy_engine"]
            self.config.strategy_engine = StrategyEngineConfig(
                node_id=strategy_engine_dict.get("node_id", self.config.strategy_engine.node_id)
                # auto_load_strategies=strategy_engine_dict.get("auto_load_strategies", self.config.strategy_engine.auto_load_strategies)
            )

        # 更新 log 配置
        if "log" in config_dict:
            log_dict = config_dict["log"]
            self.config.log = LogConfig(
                level=log_dict.get("level", self.config.log.level),
                file=log_dict.get("file", self.config.log.file)
            )

    def save(self) -> None:
        """保存配置文件"""
        if not self.config_file:
            logger.warning("未指定配置文件路径，无法保存配置")
            return
        try:
            # 将配置对象转换为字典
            config_dict = {
                "front_api": asdict(self.config.front_api),
                "http_server": asdict(self.config.http_server),
                "strategy_engine": asdict(self.config.strategy_engine),
                "log": asdict(self.config.log)
            }

            with open(self.config_file, "w") as f:
                json.dump(config_dict, f, indent=4)
            logger.info(f"配置保存到 {self.config_file} 成功")
        except Exception as e:
            logger.error(f"配置保存到 {self.config_file} 失败: {e}")

    # 特定配置获取方法
    def get_front_api_config(self) -> FrontApiConfig:
        """获取Front API配置"""
        return self.config.front_api

    def get_http_server_config(self) -> HttpServerConfig:
        """获取HTTP服务器配置"""
        return self.config.http_server

    def get_strategy_engine_config(self) -> StrategyEngineConfig:
        """获取策略引擎配置"""
        return self.config.strategy_engine

    def get_log_config(self) -> LogConfig:
        """获取日志配置"""
        return self.config.log

    def get_node_id(self) -> int:
        """获取节点ID"""
        return self.config.strategy_engine.node_id
