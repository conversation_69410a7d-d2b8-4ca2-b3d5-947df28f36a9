import json
import os
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict, field
from pathlib import Path
from utils.flog import flogger, flog_init

@dataclass
class FrontApiConfig:
    """Front API Configuration"""
    pub_address: str = "unix:////tmp/pengyc/MM_TOPIC_FRONT_SUMMARY_0_ALL"
    req_address: str = "unix:////tmp/pengyc/MM_TOPIC_FRONT_SERVICE_0_ALL"
    log_level: str = "info"
    log_dir: str = "./log/api.log"

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)    

@dataclass
class HttpServerConfig:
    """HTTP Server Configuration"""
    host: str = "0.0.0.0"
    port: int = 5000

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class StrategyEngineConfig:
    """Strategy Engine Configuration"""
    node_id: int = 1

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class LogConfig:
    """Log Configuration"""
    level: str = "info"
    file: str = "./log/py_fb_strategy.log"

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class AppConfig:
    """Application Configuration"""
    front_api: FrontApiConfig = field(default_factory=FrontApiConfig)
    http_server: HttpServerConfig = field(default_factory=HttpServerConfig)
    strategy_engine: StrategyEngineConfig = field(default_factory=StrategyEngineConfig)
    log: LogConfig = field(default_factory=LogConfig)

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class Config:
    
    _instance = None

    @classmethod
    def get_instance(cls, config_file: str = None):
        if cls._instance is None:
            cls._instance = cls(config_file)
        return cls._instance

    def __init__(self, config_file: str = None):
        self.config_file = config_file
        self.config = AppConfig()
        self.load()

    def load(self) -> None:
        """load config"""
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r") as f:
                    config_dict = json.load(f)
                self._update_config_from_dict(config_dict)
                print(f"successfully loaded configuration from {self.config_file}")
            except Exception as e:
                print(f"failed to load configuration from {self.config_file}: {e}")
                self.config = AppConfig()
                self.save()
        else:
            if self.config_file:
                print(f"configuration file {self.config_file} dose not exist, using default configuration")
            else:
                print(f"no configuration file specified, using default configuration")
            self.config = AppConfig()
            self.save()
        self._configure_logging()
        flogger.info("init struct flogger")

    def _configure_logging(self):
        log_config = self.config.log
        log_level = log_config.level
        flog_init(log_config.file, log_level)

    def _update_config_from_dict(self, config_dict: Dict[str, Any]):
        # update front_api config
        if "front_api" in config_dict:
            front_api_dict = config_dict["front_api"]
            self.config.front_api = FrontApiConfig(
                pub_address=front_api_dict.get("pub_address", self.config.front_api.pub_address),
                req_address=front_api_dict.get("req_address", self.config.front_api.req_address),
                log_level=front_api_dict.get("log_level", self.config.front_api.log_level),
                log_dir=front_api_dict.get("log_dir", self.config.front_api.log_dir)
            )

        # update http_server config
        if "http_server" in config_dict:
            http_server_dict = config_dict["http_server"]
            self.config.http_server = HttpServerConfig(
                host=http_server_dict.get("host", self.config.http_server.host),
                port=http_server_dict.get("port", self.config.http_server.port)
            )

        # update strategy_engine config
        if "strategy_engine" in config_dict:
            strategy_engine_dict = config_dict["strategy_engine"]
            self.config.strategy_engine = StrategyEngineConfig(
                node_id=strategy_engine_dict.get("node_id", self.config.strategy_engine.node_id)
                # auto_load_strategies=strategy_engine_dict.get("auto_load_strategies", self.config.strategy_engine.auto_load_strategies)
            )

        # update log config
        if "log" in config_dict:
            log_dict = config_dict["log"]
            self.config.log = LogConfig(
                level=log_dict.get("level", self.config.log.level),
                file=log_dict.get("file", self.config.log.file)
            )

    def save(self) -> None:
        """save config"""
        if not self.config_file:
            print("no congiguration file path specified, cannot save configuration")
            return
            
        try:
            config_dict = {
                "front_api": asdict(self.config.front_api),
                "http_server": asdict(self.config.http_server),
                "strategy_engine": asdict(self.config.strategy_engine),
                "log": asdict(self.config.log)
            }
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, "w") as f:
                json.dump(config_dict, f, indent=4)
        except Exception as e:
            print(f"failed to save configuration to  {self.config_file}: {e}")


    def get_front_api_config(self) -> FrontApiConfig:
        return self.config.front_api

    def get_http_server_config(self) -> HttpServerConfig:
        return self.config.http_server

    def get_strategy_engine_config(self) -> StrategyEngineConfig:
        return self.config.strategy_engine

    def get_log_config(self) -> LogConfig:
        return self.config.log

    def get_node_id(self) -> int:
        return self.config.strategy_engine.node_id
