#!/bin/bash

# 设置API基础URL
API_URL="http://localhost:5000/api"

# 检查策略ID文件是否存在
if [ ! -f ".strategy_id" ]; then
    echo "错误: 找不到策略ID文件 (.strategy_id)"
    echo "请确保已经运行过 start_market_logger.sh 脚本"
    exit 1
fi

# 从文件读取策略ID
STRATEGY_ID=$(cat .strategy_id)

# 检查策略ID是否为空
if [ -z "$STRATEGY_ID" ]; then
    echo "错误: 策略ID为空"
    echo "请检查 .strategy_id 文件内容"
    exit 1
fi

echo "准备停止策略，策略ID: $STRATEGY_ID"

# 停止策略
echo "停止策略..."
STOP_RESPONSE=$(curl -s -X POST "$API_URL/strategy/stop" \
  -H "Content-Type: application/json" \
  -d "{\"strategy_id\": $STRATEGY_ID}")
echo $STOP_RESPONSE

# 检查停止是否成功
if echo "$STOP_RESPONSE" | grep -q "success\|成功"; then
    echo "策略停止成功"
    # 清理策略ID文件
    rm -f .strategy_id
    echo "已清理策略ID文件"
else
    echo "策略停止可能失败，请检查响应信息"
    echo "策略ID文件保留，可以手动重试停止"
fi

echo "操作完成"
