# py_fb_strategy

Python 版本的策略引擎，用于替代 C++ 版本的 fb_strategy 模块。

## 项目结构

```
py_fb_strategy_new/
├── common/               # 通用模块
│   ├── __init__.py
│   ├── event.py          # 事件引擎
│   ├── front_api.py      # Front API 接口
│   ├── id_generator.py   # ID 生成器
│   ├── mdb.py            # 内存数据库
│   └── table.py          # 表格定义
├── engine/               # 引擎模块
│   ├── __init__.py
│   ├── strategy_caller.py  # 策略调用器
│   ├── strategy_engine.py  # 策略引擎
│   ├── strategy_factory.py # 策略工厂
│   └── strategy_stream.py  # 策略数据流
├── py_strategy_api/      # 策略 API 模块
│   ├── __init__.py
│   ├── enum.py           # 枚举定义
│   ├── models.py         # 数据模型
│   └── strategy_api.py   # 策略 API
├── strategies/           # 策略模块
│   ├── __init__.py
│   └── demo_strategy.py  # 示例策略
├── utils/                # 工具模块
│   ├── __init__.py
│   ├── constants.py      # 常量定义
│   └── logger.py         # 日志工具
├── web/                  # Web 相关模块
│   ├── __init__.py
│   ├── http_response.py  # HTTP 响应
│   ├── http_server.py    # HTTP 服务器
│   └── strategy_publisher.py # 策略发布器
├── config.py             # 配置管理
├── main.py               # 主入口文件
├── README.md             # 项目说明
└── requirements.txt      # 依赖列表
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行

```bash
python main.py --config config.json
```

## 开发新策略

1. 在 `strategies` 目录下创建新的策略文件
2. 继承 `py_strategy_api.strategy_api.StrategyApi` 类
3. 实现必要的方法，如 `on_init`、`on_start`、`on_stop`、`on_market_data` 等
4. 使用 HTTP API 加载和管理策略

## HTTP API

- `POST /api/strategy/load`: 加载策略
- `POST /api/strategy/create`: 创建策略
- `POST /api/strategy/start`: 启动策略
- `POST /api/strategy/stop`: 停止策略
- `POST /api/strategy/delete`: 删除策略
- `GET /api/strategy/list`: 获取策略列表
- `GET /api/strategy/{strategy_id}`: 获取策略信息
- `GET /api/strategy/{strategy_id}/parameters`: 获取策略参数
- `POST /api/strategy/parameter/update`: 更新策略参数
- `GET /api/health`: 健康检查
