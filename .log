{'rsp_info': FrontApiMsg(name='rsp_info', field='rsp_info', comment='通用响应消息', entity=None, item=[Item(name='error_id', type='number_type', description=None), Item(name='error_msg', type='error_msg_type', description=None)]), 'market_data': FrontApiMsg(name='market_data', field='market_data', comment='原始市场行情', entity=None, item=[Item(name='instrument_id', type='instrument_id_type', description='合约id'), Item(name='exchange_id', type='exchange_id_type', description='交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部'), Item(name='update_sec', type='second_type', description='更新秒'), Item(name='update_msec', type='mil_second_type', description='更新毫秒'), Item(name='pre_settlement', type='price_type', description='昨结算价'), Item(name='pre_close', type='price_type', description='昨收盘价'), Item(name='open', type='price_type', description='开盘价'), Item(name='close', type='price_type', description='收盘价'), Item(name='upper_limit_price', type='price_type', description='涨停价'), Item(name='down_limit_price', type='price_type', description='跌停价'), Item(name='high_price', type='price_type', description='最高价'), Item(name='low_price', type='price_type', description='最低价'), Item(name='last_price', type='price_type', description='最新价'), Item(name='volume', type='volume_type', description='累计成交量'), Item(name='turn_over', type='turn_over_type', description='累计成交金额'), Item(name='pre_open_interest', type='open_interest_type', description='昨持仓量'), Item(name='open_interest', type='open_interest_type', description='持仓量'), Item(name='bid1_price', type='price_type', description='买一价'), Item(name='ask1_price', type='price_type', description='卖一价'), Item(name='bid1_volume', type='volume_type', description='买一量'), Item(name='ask1_volume', type='volume_type', description='卖一量'), Item(name='bid2_price', type='price_type', description='买二价'), Item(name='ask2_price', type='price_type', description='卖二价'), Item(name='bid2_volume', type='volume_type', description='买二量'), Item(name='ask2_volume', type='volume_type', description='卖二量'), Item(name='bid3_price', type='price_type', description='买三价'), Item(name='ask3_price', type='price_type', description='卖三价'), Item(name='bid3_volume', type='volume_type', description='买三量'), Item(name='ask3_volume', type='volume_type', description='卖三量'), Item(name='bid4_price', type='price_type', description='买四价'), Item(name='ask4_price', type='price_type', description='卖四价'), Item(name='bid4_volume', type='volume_type', description='买四量'), Item(name='ask4_volume', type='volume_type', description='卖四量'), Item(name='bid5_price', type='price_type', description='买五价'), Item(name='ask5_price', type='price_type', description='卖五价'), Item(name='bid5_volume', type='volume_type', description='买五量'), Item(name='ask5_volume', type='volume_type', description='卖五量'), Item(name='bid6_price', type='price_type', description='买六价'), Item(name='ask6_price', type='price_type', description='卖六价'), Item(name='bid6_volume', type='volume_type', description='买六量'), Item(name='ask6_volume', type='volume_type', description='卖六量'), Item(name='bid7_price', type='price_type', description='买七价'), Item(name='ask7_price', type='price_type', description='卖七价'), Item(name='bid7_volume', type='volume_type', description='买七量'), Item(name='ask7_volume', type='volume_type', description='卖七量'), Item(name='bid8_price', type='price_type', description='买八价'), Item(name='ask8_price', type='price_type', description='卖八价'), Item(name='bid8_volume', type='volume_type', description='买八量'), Item(name='ask8_volume', type='volume_type', description='卖八量'), Item(name='bid9_price', type='price_type', description='买九价'), Item(name='ask9_price', type='price_type', description='卖九价'), Item(name='bid9_volume', type='volume_type', description='买九量'), Item(name='ask9_volume', type='volume_type', description='卖九量'), Item(name='bid10_price', type='price_type', description='买十价'), Item(name='ask10_price', type='price_type', description='卖十价'), Item(name='bid10_volume', type='volume_type', description='买十量'), Item(name='ask10_volume', type='volume_type', description='卖十量'), Item(name='iopv', type='price_type', description='基金价格算法得出的价格（证券行情）'), Item(name='dynamic_reference_price', type='price_type', description='动态参考价（证券行情）'), Item(name='local_timestamp', type='timestamp_type', description='timestamp')]), 'position': FrontApiMsg(name='position', field='position', comment='合约持仓信息维护', entity=None, item=[Item(name='instrument_id', type='instrument_id_type', description='合约id'), Item(name='trading_account_id', type='trading_account_id_type', description='交易账户id'), Item(name='long_position', type='volume_type', description='多仓'), Item(name='short_position', type='volume_type', description='空仓'), Item(name='frozen_long_position', type='volume_type', description='冻结多仓'), Item(name='frozen_short_position', type='volume_type', description='冻结空仓'), Item(name='td_long_position', type='volume_type', description='今多仓'), Item(name='td_short_position', type='volume_type', description='今空仓'), Item(name='frozen_td_long_position', type='volume_type', description='冻结今多仓'), Item(name='frozen_td_short_position', type='volume_type', description='冻结今空仓'), Item(name='yd_long_position', type='volume_type', description='昨多仓'), Item(name='yd_short_position', type='volume_type', description='昨空仓'), Item(name='frozen_yd_long_position', type='volume_type', description='冻结昨多仓'), Item(name='frozen_yd_short_position', type='volume_type', description='冻结昨空仓'), Item(name='long_comb_position', type='volume_type', description='组合多仓'), Item(name='short_comb_position', type='volume_type', description='组合空仓'), Item(name='buy_open_position', type='volume_type', description='买开仓'), Item(name='option_exec_position', type='volume_type', description='期权行权仓'), Item(name='option_abandon_position', type='volume_type', description='期权放弃行权仓'), Item(name='td_option_exec_position', type='volume_type', description='今期权行权仓'), Item(name='td_option_abandon_position', type='volume_type', description='今期权放弃行权仓'), Item(name='yd_option_exec_position', type='volume_type', description='昨期权行权仓'), Item(name='yd_option_abandon_position', type='volume_type', description='昨期权放弃行权仓'), Item(name='sell_open_position', type='volume_type', description='卖开仓'), Item(name='frozen_sell_open_position', type='volume_type', description='在途卖开仓量'), Item(name='frozen_buy_open_position', type='volume_type', description='在途买开仓量')]), 'order': FrontApiMsg(name='order', field='order', comment='order指令流水', entity=FrontApiMsgItem(item=[Item(name='instrument_id', type='instrument_id_type', description='合约id'), Item(name='direction', type='direction_type', description='买卖方向，0-买，1-卖'), Item(name='offset_flag', type='offset_flag_type', description='开平标志，1-开，2-平，3-平今，4-平昨，5-自动开平'), Item(name='hedge_flag', type='hedge_flag_type', description='投机套保标志，1-投机，2-套利，3-套保，4-做市商'), Item(name='price', type='price_type', description='成交价'), Item(name='volume', type='volume_type', description='累计成交量'), Item(name='price_category', type='price_category_type', description='价格类型，1-市价，2-限价，3-对手方最优价，4-最优五档，5-套利，6-互换，7-报价衍生，8-其他，9-本方最优价'), Item(name='time_condition', type='time_condition_type', description='时间类型，1-IOC，2-GFD, 3-GIS'), Item(name='volume_condition', type='volume_condition_type', description='成交数量类型，1-部分成交，2-全部成交'), Item(name='portfolio_id', type='portfolio_id_type', description='组合id'), Item(name='order_id', type='order_id_type', description='飞豹报单编号'), Item(name='priority', type='priority_type', description='优先级'), Item(name='custom_flag', type='custom_flag_type', description='自定义标识'), Item(name='seat_no', type='seat_no_type', description='席位号'), Item(name='trading_account_id', type='trading_account_id_type', description='交易账户id'), Item(name='investor_id', type='investor_account_id_type', description='资金账户id')]), item=[Item(name='order_sys_id', type='order_sys_id_type', description='交易所报单编号'), Item(name='order_status', type='order_status_type', description='报单状态，0-未知状态，1-已报单，待确认，2-交易所确认，3-订单取消，4-订单错误，5-部分成交，6-全部成交，7-超时状态'), Item(name='user_id', type='user_id_type', description='用户名关联id'), Item(name='action_user_id', type='user_id_type', description='报单操作用户id'), Item(name='insert_time', type='second_type', description='交易所插入时间'), Item(name='update_time', type='second_type', description='交易所更新时间'), Item(name='local_create_time', type='macro_sec_type', description='服务器创建时间'), Item(name='local_insert_time', type='macro_sec_type', description='服务器插入时间'), Item(name='local_update_time', type='macro_sec_type', description='服务器更新时间'), Item(name='exchange_id', type='exchange_id_type', description='交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部'), Item(name='traded_volume', type='volume_type', description='成交量'), Item(name='cancel_volume', type='volume_type', description='撤单量'), Item(name='order_source', type='order_source_type', description='报单来源，1-手动报单，2-手动报价单，3-策略报单，4-策略报价单，5-外部报单，6-外部报价单，A-全部来源'), Item(name='strategy_instance_id', type='strategy_instance_id_type', description='策略实例id'), Item(name='last_operator_id', type='user_id_type', description='最近操作人id'), Item(name='error_id', type='error_id_type', description='错误id')]), 'user': FrontApiMsg(name='user', field='user', comment='用户、用户角色基础信息，', entity=None, item=[Item(name='user_id', type='user_id_type', description='用户名关联id'), Item(name='user_name', type='user_name_type', description='登录用户名'), Item(name='user_role', type='user_type_type', description='用户绑定角色信息，1-管理员类型，2-交易员类型，3-风控员类型，4-查询员类型，5-账户管理员类型，'), Item(name='default_trading_account_id', type='trading_account_id_type', description='默认交易账户，用于客户端打开默认展示'), Item(name='active_flag', type='int_status_type', description='账户正常/冻结标识'), Item(name='status', type='int_status_type', description='记录有效状态，48-无效，49-有效'), Item(name='last_operator_id', type='user_id_type', description='最近操作人id'), Item(name='create_time', type='date_time_type', description='账户创建时间')]), 'user_password': FrontApiMsg(name='user_password', field='user_password', comment='用户对应密码信息，用于登录用户认证', entity=None, item=[Item(name='user_id', type='user_id_type', description='用户名关联id'), Item(name='password', type='password_type', description='用户密码'), Item(name='last_operator_id', type='user_id_type', description='最近操作人id')]), 'trading_account': FrontApiMsg(name='trading_account', field='trading_account', comment='交易账户id和名称维护', entity=None, item=[Item(name='trading_account_id', type='trading_account_id_type', description='交易账户id'), Item(name='trading_account_name', type='user_name_type', description='交易账户名'), Item(name='status', type='int_status_type', description='记录有效状态，48-无效，49-有效')]), 'trader_account_config': FrontApiMsg(name='trader_account_config', field='trader_account_config', comment='用户绑定交易账户关系，用于数据读取权限', entity=None, item=[Item(name='user_id', type='user_id_type', description='用户名关联id'), Item(name='trading_account_id', type='trading_account_id_type', description='交易账户id'), Item(name='last_operator_id', type='user_id_type', description='最近操作人id'), Item(name='status', type='int_status_type', description='记录有效状态，48-无效，49-有效')]), 'security_instrument': FrontApiMsg(name='security_instrument', field='security_instrument', comment='现货合约信息及基础属性', entity=None, item=[Item(name='exchange_id', type='exchange_id_type', description='交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部'), Item(name='product_id', type='product_id_type', description='合约对应品种id'), Item(name='instrument_id', type='instrument_id_type', description='合约id'), Item(name='instrument_name', type='instrument_name_type', description='合约名'), Item(name='security_class', type='security_class_type', description='现货种类，0-基金，1-股票，2-债券，n-未配置'), Item(name='security_sub_class', type='security_sub_class_type', description='现货子种类，0-ETF基金，1-RET基金，2-科创板股票，3-新三板股票， 4-精选层股票， n-未配置'), Item(name='market_maker_count', type='count_type', description='做市商数量'), Item(name='multiple', type='multiple_type', description='合约乘数'), Item(name='tick', type='tick_type', description='最小变动价位'), Item(name='unit', type='unit_type', description='最小报价单位'), Item(name='trading_type', type='trading_type_type', description='交易类型，0-T+0,1-T+1,n-未配置'), Item(name='status', type='int_status_type', description='记录有效状态，48-无效，49-有效'), Item(name='last_operator_id', type='user_id_type', description='最近操作人id')]), 'future_instrument': FrontApiMsg(name='future_instrument', field='future_instrument', comment='期货合约信息及基础属性', entity=None, item=[Item(name='exchange_id', type='exchange_id_type', description='交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部'), Item(name='product_id', type='product_id_type', description='品种id'), Item(name='instrument_id', type='instrument_id_type', description='合约id'), Item(name='instrument_name', type='instrument_name_type', description='合约名'), Item(name='multiple', type='multiple_type', description='合约乘数'), Item(name='tick', type='tick_type', description='最小变动价位'), Item(name='expire_date', type='date_type', description='到期日'), Item(name='expire_date_type', type='expire_date_type_type', description='到期日类型，1-当月合约，2-远月合约'), Item(name='exercise_type', type='exercise_type_type', description='交割方式，1-实物交割，2-现金交割 默认2'), Item(name='is_dominant', type='int_status_type', description='期货主力合约标注'), Item(name='status', type='int_status_type', description='记录有效状态，48-无效，49-有效'), Item(name='last_operator_id', type='user_id_type', description='最近操作人id')]), 'option_instrument': FrontApiMsg(name='option_instrument', field='option_instrument', comment='期权合约信息及基础属性', entity=None, item=[Item(name='exchange_id', type='exchange_id_type', description='交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部'), Item(name='product_id', type='product_id_type', description='品种id'), Item(name='option_serial_id', type='option_serial_id_type', description='期权系列id'), Item(name='instrument_id', type='instrument_id_type', description='合约id'), Item(name='instrument_name', type='instrument_name_type', description='合约名'), Item(name='multiple', type='multiple_type', description='合约乘数'), Item(name='tick', type='tick_type', description='最小变动价位'), Item(name='strike_price', type='price_type', description='行权价'), Item(name='expire_date', type='date_type', description='到期日'), Item(name='option_type', type='option_type_type', description='期权合约类型，c-call，p-put'), Item(name='expire_date_type', type='expire_date_type_type', description='到期日类型，1-当月合约，2-远月合约'), Item(name='underlying_instrument_id', type='instrument_id_type', description='标的合约id'), Item(name='exercise_type', type='exercise_type_type', description='交割方式，1-实物交割，2-现金交割 默认2'), Item(name='exercise_style', type='exercise_style_type', description='行权方式，0-美式，1-欧式 默认0'), Item(name='status', type='int_status_type', description='记录有效状态，48-无效，49-有效'), Item(name='last_operator_id', type='user_id_type', description='最近操作人id')]), 'instrument_param_value': FrontApiMsg(name='instrument_param_value', field='instrument_param_value', comment='合约参数参数值', entity=FrontApiMsgItem(item=[Item(name='instrument_id', type='instrument_id_type', description='合约id'), Item(name='trading_account_id', type='trading_account_id_type', description='交易账户id'), Item(name='param_key', type='param_key_type', description='参数名'), Item(name='param_value', type='param_value_type', description='参数值'), Item(name='last_operator_id', type='user_id_type', description='最近操作人id'), Item(name='last_operate_source', type='last_operate_source_type', description='最近操作来源，0-客户端，1-策略'), Item(name='status', type='int_status_type', description='记录有效状态，48-无效，49-有效')]), item=None), 'instrument_param_define': FrontApiMsg(name='instrument_param_define', field='instrument_param_define', comment='合约参数定义', entity=None, item=[Item(name='trading_account_id', type='trading_account_id_type', description='交易账户id'), Item(name='param_key', type='param_key_type', description='参数名'), Item(name='param_type', type='param_type_type', description='参数类型'), Item(name='authority_category', type='authority_category_type', description='权限类别，0-策略参数只读属性，1-策略参数读写属性'), Item(name='enum_define', type='enum_define_type', description='枚举定义'), Item(name='last_operator_id', type='user_id_type', description='最近操作人id'), Item(name='status', type='int_status_type', description='记录有效状态，48-无效，49-有效')])}
