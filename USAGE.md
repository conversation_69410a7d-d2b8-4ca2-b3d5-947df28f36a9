# 策略中台服务使用指南

## 快速开始

### 1. 安装依赖

```bash
# 安装策略中台服务依赖
pip install -r strategy_mid_server/requirements.txt

# 或者安装到现有环境
pip install fastapi websockets aiohttp pydantic uvicorn
```

### 2. 启动策略引擎（如果还没启动）

```bash
# 在项目根目录启动策略引擎
python main.py
```

策略引擎默认运行在 `http://localhost:5000`

### 3. 启动策略中台服务

```bash
# 方式1: 使用启动脚本（推荐）
python start_mid_server.py

# 方式2: 直接运行
python strategy_mid_server/main.py
```

服务启动后会显示：
```
================================================================================
                    策略中台服务 (Strategy Mid Server)
                           Version 1.0.0
================================================================================
配置信息:
  WebSocket服务器: 0.0.0.0:8765
  最大连接数: 100
  策略引擎地址: localhost:5000
  数据获取间隔: 30秒
  心跳间隔: 60秒
  日志级别: INFO
  日志文件: ./log/mid_server.log
--------------------------------------------------------------------------------
正在启动策略中台服务...
服务启动中，按 Ctrl+C 停止服务
================================================================================
```

### 4. 测试WebSocket连接

```bash
# 使用提供的测试客户端
python test_websocket_client.py

# 或者指定不同的地址
python test_websocket_client.py ws://localhost:8765
```

## 服务功能验证

### 1. WebSocket连接测试

运行测试客户端后，应该看到类似输出：
```
============================================================
WebSocket客户端测试工具
============================================================
正在连接到 ws://localhost:8765...
连接成功!
发送消息: {'type': 'subscribe', 'tables': ['strategy_enum_field', 'strategy_deploy_field', 'strategy_param_field']}
发送消息: {'type': 'ping'}
开始监听消息...
------------------------------------------------------------
[14:30:15] 收到消息类型: welcome
  欢迎消息: Connected to Strategy Mid Server
------------------------------------------------------------
[14:30:15] 收到消息类型: subscribe_ack
  数据: {'type': 'subscribe_ack', 'tables': ['strategy_enum_field', 'strategy_deploy_field', 'strategy_param_field'], 'status': 'success'}
------------------------------------------------------------
[14:30:15] 收到消息类型: pong
  Pong响应
------------------------------------------------------------
[14:30:45] 收到消息类型: snap
  表名: strategy_enum_field
  数据条数: 9
  是否最后一个快照: 是
  示例数据:
    1: exfuture_1_std_future_quote,hedge_type,0:tick;1:abs;2:percent,1
    2: exfuture_1_std_future_quote,status_type,0:inactive;1:active,1
    3: exfuture_1_std_future_quote,quote_mode_type,0:order;1:normal_quote;2:pop_quote,1
    ... 还有 6 条数据
------------------------------------------------------------
```

### 2. 数据推送验证

中台服务会定期（默认30秒）推送三种类型的数据：

1. **strategy_enum_field**: 策略枚举字段
2. **strategy_deploy_field**: 策略部署字段  
3. **strategy_param_field**: 策略参数字段

每种数据的格式如需求文档所示。

### 3. 错误处理测试

#### 测试策略引擎不可用的情况：

1. 停止策略引擎服务
2. 观察中台服务日志，应该看到：
   ```
   WARNING - Strategy engine health check failed, using mock data
   INFO - Broadcasted mock data to clients
   ```
3. 客户端仍然能收到模拟数据

#### 测试网络重连：

1. 断开测试客户端
2. 重新连接
3. 应该能正常接收数据

## 配置自定义

### 修改配置文件

编辑 `strategy_mid_server/config/config.json`：

```json
{
    "websocket_host": "0.0.0.0",
    "websocket_port": 8765,
    "max_connections": 100,
    "strategy_engine_host": "localhost",
    "strategy_engine_port": 5000,
    "strategy_engine_timeout": 30,
    "fetch_interval": 10,          // 改为10秒获取一次
    "heartbeat_interval": 30,      // 改为30秒心跳一次
    "retry_interval": 5,           // 改为5秒重试间隔
    "max_retries": 5,              // 改为最多重试5次
    "log_level": "DEBUG",          // 改为DEBUG级别日志
    "log_file": "./log/mid_server.log"
}
```

### 常用配置场景

#### 开发环境配置
```json
{
    "fetch_interval": 10,
    "log_level": "DEBUG",
    "max_retries": 1
}
```

#### 生产环境配置
```json
{
    "fetch_interval": 60,
    "log_level": "INFO",
    "max_retries": 5,
    "max_connections": 1000
}
```

## 监控和日志

### 日志文件位置
- 默认日志文件: `./log/mid_server.log`
- 控制台也会输出日志

### 关键日志信息

#### 正常运行日志
```
INFO - Starting Strategy Mid Server
INFO - WebSocket server started successfully
INFO - Strategy data scheduler started
INFO - Broadcasted 3 messages to clients
```

#### 错误日志
```
WARNING - Strategy engine health check failed, using mock data
ERROR - Error fetching and broadcasting data: ...
WARNING - Max connections reached, rejecting client
```

### 服务状态监控

可以通过日志监控以下指标：
- 客户端连接数
- 数据获取成功率
- 重试次数
- 消息推送数量

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查端口是否被占用
   - 检查防火墙设置
   - 确认服务是否正常启动

2. **策略引擎连接失败**
   - 检查策略引擎是否运行
   - 检查网络连接
   - 验证端口配置

3. **数据不更新**
   - 检查fetch_interval配置
   - 查看错误日志
   - 验证策略引擎API是否正常

4. **内存使用过高**
   - 检查客户端连接数
   - 调整max_connections
   - 检查是否有内存泄漏

### 调试模式

启用DEBUG日志级别可以看到更详细的信息：
```json
{
    "log_level": "DEBUG"
}
```

## 集成到现有系统

### 作为服务运行

可以使用systemd、supervisor等工具将中台服务作为系统服务运行：

```bash
# 示例systemd服务文件
[Unit]
Description=Strategy Mid Server
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/py_fb_strategy
ExecStart=/usr/bin/python3 start_mid_server.py
Restart=always

[Install]
WantedBy=multi-user.target
```

### Docker部署

可以创建Dockerfile进行容器化部署：

```dockerfile
FROM python:3.8-slim

WORKDIR /app
COPY . .
RUN pip install -r strategy_mid_server/requirements.txt

EXPOSE 8765
CMD ["python", "start_mid_server.py"]
```
