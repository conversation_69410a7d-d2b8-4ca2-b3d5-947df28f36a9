from pydantic import BaseModel
from typing import Type, Any, get_type_hints


def convert_model(source_model: BaseModel, tartget_model: Type[BaseModel], **kwargs: Any) -> BaseModel:
    tartget_fields = get_type_hints(tartget_model)
    source_data = source_model.dict()
    new_data = {}
    for key in tartget_fields:
        if key in source_data:
            new_data[key] = source_data[key]
    new_data.update(kwargs)
    return tartget_model(**new_data)
