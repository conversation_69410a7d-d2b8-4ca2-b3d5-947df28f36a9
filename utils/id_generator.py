import os
import threading

from utils.flog import flogger


class IdGenerator:
    """通用ID生成器，生成唯一的ID"""

    _instance = None
    _lock = threading.Lock()
    _global_id = 0  # 用于订单和报价
    _global_comb_id = 0  # 用于组合
    _mark_file = "./id_mark.log"  # 单个文件存储所有ID

    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def __init__(self):
        """初始化"""
        self._load_global_ids()

    def _load_global_ids(self):
        """从文件中加载全局ID"""
        if os.path.exists(self._mark_file):
            try:
                with open(self._mark_file, "r") as f:
                    lines = f.readlines()
                    if len(lines) >= 1:
                        self._global_id = int(lines[0].strip() or 0)
                    if len(lines) >= 2:
                        self._global_comb_id = int(lines[1].strip() or 0)
            except Exception as e:
                flogger.error(f"加载全局ID失败: {e}")

    def _save_global_ids(self):
        """保存全局ID到文件"""
        try:
            with open(self._mark_file, "w") as f:
                f.write(f"{self._global_id}\n{self._global_comb_id}\n")
        except Exception as e:
            flogger.error(f"保存全局ID失败: {e}")

    def get_order_id(self, node_id: int) -> int:
        """生成订单ID

        Args:
            node_id: 节点ID

        Returns:
            int: 订单ID
        """
        with self._lock:
            self._global_id += 1
            # 生成订单ID: (node_id << 32) | ((global_id << 2) | 0x3)
            order_id = (node_id << 32) | ((self._global_id << 2) | 0x3)
            self._save_global_ids()
            return order_id

    def get_quote_id(self, node_id: int) -> int:
        """生成报价ID

        Args:
            node_id: 节点ID

        Returns:
            int: 报价ID
        """
        with self._lock:
            self._global_id += 1
            # 生成报价ID: (node_id << 32) | (global_id << 2)
            quote_id = (node_id << 32) | (self._global_id << 2)
            self._save_global_ids()
            return quote_id

    def get_comb_id(self, node_id: int) -> int:
        """生成组合ID

        Args:
            node_id: 节点ID

        Returns:
            int: 组合ID
        """
        with self._lock:
            self._global_comb_id += 1
            # 生成组合ID: (node_id << 32) | global_id
            comb_id = (node_id << 32) | self._global_comb_id
            self._save_global_ids()
            return comb_id

    def is_order(self, order_id: int) -> bool:
        """判断是否为订单ID

        Args:
            order_id: 订单ID

        Returns:
            bool: 是否为订单ID
        """
        return (order_id & 0x3) == 0x3

    def is_quote(self, order_id: int) -> bool:
        """判断是否为报价ID

        Args:
            order_id: 订单ID

        Returns:
            bool: 是否为报价ID
        """
        return (order_id & 0x3) != 0x3

    def is_quote_bid(self, order_id: int) -> bool:
        """判断是否为报价买单ID

        Args:
            order_id: 订单ID

        Returns:
            bool: 是否为报价买单ID
        """
        return ((order_id & 0x1) == 0x1) and ((order_id & 0x3) != 0x3)

    def is_quote_ask(self, order_id: int) -> bool:
        """判断是否为报价卖单ID

        Args:
            order_id: 订单ID

        Returns:
            bool: 是否为报价卖单ID
        """
        return ((order_id & 0x2) == 0x2) and ((order_id & 0x3) != 0x3)

    def convert_to_quote_id(self, order_id: int) -> int:
        """将订单ID转换为报价ID

        Args:
            order_id: 订单ID

        Returns:
            int: 报价ID
        """
        return (order_id >> 2) << 2

    def convert_to_bid_order_id(self, quote_id: int) -> int:
        """将报价ID转换为买单ID

        Args:
            quote_id: 报价ID

        Returns:
            int: 买单ID
        """
        return quote_id | 0x1

    def convert_to_ask_order_id(self, quote_id: int) -> int:
        """将报价ID转换为卖单ID

        Args:
            quote_id: 报价ID

        Returns:
            int: 卖单ID
        """
        return quote_id | 0x2

    def convert_to_other_side_order_id(self, order_id: int) -> int:
        """将订单ID转换为另一侧订单ID

        Args:
            order_id: 订单ID

        Returns:
            int: 另一侧订单ID
        """
        return ((order_id >> 2) << 2) | ((order_id & 0x3) ^ 0x3)

    def reset(self):
        """重置全局ID"""
        with self._lock:
            self._global_id = 0
            self._global_comb_id = 0
            self._save_global_ids()


# 兼容性别名
OrderIdGenerator = IdGenerator
