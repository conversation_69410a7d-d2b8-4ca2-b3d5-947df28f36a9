import logging
from py_strategy_api.strategy_api import StrategyApi
from py_strategy_api.models import MarketDataMsg

class MarketLoggerStrategy(StrategyApi):

    strategy_verison = "1.0.0"
    instrument_id = "CFE_IF2412"  # 合约ID
    parameters = ["instrument_id"]

    def __init__(self, engine):
        super().__init__(engine)
        self._engine = engine
        
        # 统计信息
        self.tick_count = 0

    def on_init(self):
        """策略初始化"""
        print(f"策略 {self.get_name()} 初始化")
        print(f"监控合约: {self.instrument_id}")

    def on_start(self):
        """策略启动"""
        super().on_start()
        print(f"策略 {self.get_name()} 启动")
        self.tick_count = 0

    def on_stop(self):
        """策略停止"""
        super().on_stop()
        print(f"策略 {self.get_name()} 停止，共接收 {self.tick_count} 条行情")

    def on_market_data(self, tick: MarketDataMsg):
        """市场数据回调"""
        # 只处理指定合约的行情
        if tick.instrument_id != self.instrument_id:
            return

        # 增加计数
        self.tick_count += 1
        
        # 记录行情信息
        print(
            f"行情更新 [{self.tick_count}] ",
            f"交易所={tick.exchange_id}, ",
            f"更新秒={tick.update_sec}, ",
            f"更新毫秒={tick.update_msec}, ",
            f"累计成交金额={tick.turn_over}"
        )
        
        # 每100条行情记录一次统计信息
        if self.tick_count % 100 == 0:
            print(f"已接收 {self.tick_count} 条 {self.instrument_id} 行情")

    def on_order(self, order):
        """订单回调"""
        print(f"收到订单回报: {order.order_id}, 状态: {order.order_status}")

    def on_trade(self, trade):
        """成交回调"""
        print(f"收到成交回报: {trade.trade_id}, 价格: {trade.price}, 数量: {trade.volume}")

