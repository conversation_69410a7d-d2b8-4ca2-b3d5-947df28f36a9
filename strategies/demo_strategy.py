from py_strategy_api.strategy_api import StrategyApi
from py_strategy_api.models import *
from py_strategy_api.enum import *

class DemoStrategy(StrategyApi):
    """示例策略"""
    strategy_verison = "1.0.0"
    param1 = 10  # 参数1
    param2 = 20  # 参数2
    instrument_id = "IF2106"  # 合约ID
    parameters = ["param1", "param2", "instrument_id"]

    def __init__(self, engine):
        super().__init__(engine)
        self._engine = engine
        self._last_price = 0.0

    def on_init(self):
        """策略初始化"""
        print(f"策略 {self.get_name()} 初始化")

    def on_start(self):
        """策略启动"""
        super().on_start()
        print(f"策略 {self.get_name()} 启动")

    def on_stop(self):
        """策略停止"""
        super().on_stop()
        print(f"策略 {self.get_name()} 停止")

    def on_market_data(self, tick):
        """市场数据回调"""
        if tick.instrument_id != self.instrument_id:
            return

        self._last_price = tick.last_price
        print(f"策略 {self.get_name()} 收到行情: {tick.instrument_id}, 最新价: {tick.last_price}")

        # 简单的交易逻辑
        if self._last_price > self.param1:
            self._buy()
        elif self._last_price < self.param2:
            self._sell()

    def on_order(self, order):
        """订单回调"""
        print(f"策略 {self.get_name()} 收到订单回报: {order.order_id}, 状态: {order.order_status}")

    def on_trade(self, trade):
        """成交回调"""
        print(f"策略 {self.get_name()} 收到成交回报: {trade.trade_id}, 价格: {trade.price}, 数量: {trade.volume}")

    def _buy(self):
        """买入"""
        order = OrderEntity(
            instrument_id=self.instrument_id,
            direction=DirectionEnum.DIRECTION_BUY.value,
            offset_flag=OffsetFlagEnum.OFFSET_OPEN.value,
            price=self._last_price,
            volume=1
        )
        self.send_order(order)

    def _sell(self):
        """卖出"""
        order = OrderEntity(
            instrument_id=self.instrument_id,
            direction=DirectionEnum.DIRECTION_SELL,
            offset_flag=OffsetFlagEnum.OFFSET_CLOSE,
            price=self._last_price,
            volume=1
        )
        self.send_order(order)
