from py_strategy_api.strategy_api import StrategyApi
from py_strategy_api.models import *
from py_strategy_api.enum import *
from typing import List
from common.table_enum import table_enum

class DemoOrderStrategy(StrategyApi):
    """示例策略"""
    strategy_verison = "1.0.0"
    instrument_id = ""  # 合约ID
    parameters = ["instrument_id"]

    trading_account_id: int = None

    def __init__(self, engine):
        super().__init__(engine)
        self._engine = engine

    def on_init(self):
        """策略初始化"""
        print(f"策略 {self.get_name()} 初始化")

    def on_start(self):
        """策略启动"""
        super().on_start()
        print(f"策略 {self.get_name()} 启动")
        print(f"demo_order_strategy instrument id {self.instrument_id}")

        
        data: List[TradingAccountMsg] = self.query(self._strategy_id, table_enum.trading_account)
        print("查询 trading account mdb \n", data)
        for trading_account in data:
            if trading_account.trading_account_name == "exfuture_1":
                self.trading_account_id = trading_account.trading_account_id
                print("交易账户ID", self.trading_account_id)
        
        sec_data = self.query(self._strategy_id, table_enum.security_instrument, instrument_id="SSE_510050")
        print("查询 security instrument mdb \n", sec_data, type(sec_data))

        future_data = self.query(self._strategy_id, table_enum.future_instrument)
        print("查询 future instrument mdb \n", future_data)

        option_data = self.query(self._strategy_id, table_enum.option_instrument)
        print("查询 option instrument mdb \n", option_data[0] if option_data else "empty")

        strategy_data = self.query(strategy_id = self._strategy_id, table = table_enum.strategy_instance)
        print("查询 strategy instance mdb \n", strategy_data)

    def on_stop(self):
        """策略停止"""
        super().on_stop()
        print(f"策略 {self.get_name()} 停止")

    def on_market_data(self, tick):
        """市场数据回调"""
        if tick.instrument_id != self.instrument_id:
            return

        print(f"策略 {self.get_name()} 收到行情: {tick.instrument_id}")

        self._last_order_id = self._buy()
        print("62", self._last_order_id)
        self._cancel_last_order()
        print("64", self._last_order_id)
        self._last_order_id = self._sell()
        print("66", self._last_order_id)
        self._cancel_last_order()
        print("68", self._last_order_id)

        data = self.query(strategy_id = self._strategy_id, table = table_enum.market_data, instrument_id=self.instrument_id)
        print("查询market data mdb \n", data)

        data = self.query(strategy_id = self._strategy_id, table = table_enum.position, instrument_id=self.instrument_id, trading_account_id=self.trading_account_id)
        print("查询position mdb \n", data)

    def on_position(self, positions):
        print(f"策略 {self.get_name()} 收到持仓更新: {positions}")

    def on_instrument_param_value(self, instrument_param_value: InstrumentParamValueMsg):
        print(f"策略 {self.get_name()} 收到合约参数更新: {instrument_param_value}")
        if instrument_param_value.instrument_id == "CFE_IF2412" and instrument_param_value.param_key == "test_modify":
            if instrument_param_value.param_value == "6":
                modified_instument_param_value = FbInstrumentParamValueEntity(
                    instrument_id="CFE_IF2412",
                    param_key="test_modify",
                    param_value="999"
                )
                self.modify_instrument_param_value(
                    modified_instument_param_value)

    def on_custom_param_value(self, custom_param_value: CustomParamValueMsg):
        print(f"策略 {self.get_name()} 收到自定义参数更新: {custom_param_value}")
        if custom_param_value.custom_id == "l1" and custom_param_value.param_key == "test_modify":
            if custom_param_value.param_value == "6":
                modified_custom_param_value = FbCustomParamValueEntity(
                    custom_id="l1",
                    param_key="test_modify",
                    param_value="999"
                )
                self.modify_custom_param_value(modified_custom_param_value)

    def on_order(self, order):
        """订单回调"""
        print(
            f"策略 {self.get_name()} 收到订单回报: {order.order_id}, 状态: {order.order_status}")

    def on_trade(self, trade):
        """成交回调"""
        print(
            f"策略 {self.get_name()} 收到成交回报: {trade.trade_id}, 价格: {trade.price}, 数量: {trade.volume}")

    def on_trading_account(self, trading_account: TradingAccountMsg) -> None:
        print(f"策略 {self.get_name()} 账户回报: {trading_account}")

    def _buy(self):
        """买入"""
        print(f"发送买入订单，价格: {3500}")
        order = FbOrderEntity(
            instrument_id=self.instrument_id,
            direction=DirectionEnum.DIRECTION_BUY.value,
            hedge_flag=HedgeFlagEnum.HEDGE_FLAG_MARKET_MAKER.value,
            offset_flag=OffsetFlagEnum.OFFSET_FLAG_AUTO.value,
            price=3500.00,
            price_category=PriceCategoryEnum.PRICE_CATEGORY_LIMIT.value,
            volume=5,
            time_condition=TimeConditionEnum.TIME_CONDITION_GFD.value,
            volume_condition=VolumeConditionEnum.VOLUME_ANY.value,
            portfolio_id=0,
            custom_flag='',
            seat_no='123',
            investor_id="001"
        )
        order_id = self.send_order(order)
        print(f"买入订单已发送: ID={order_id}")
        return order_id

    def _sell(self):
        """卖出"""
        print(f"发送卖出订单，价格: {3500}")
        order = FbOrderEntity(
            instrument_id=self.instrument_id,
            direction=DirectionEnum.DIRECTION_SELL.value,
            hedge_flag=HedgeFlagEnum.HEDGE_FLAG_MARKET_MAKER.value,
            offset_flag=OffsetFlagEnum.OFFSET_FLAG_AUTO.value,
            price=3500.00,
            price_category=PriceCategoryEnum.PRICE_CATEGORY_LIMIT.value,
            volume=5,
            time_condition=TimeConditionEnum.TIME_CONDITION_GFD.value,
            volume_condition=VolumeConditionEnum.VOLUME_ANY.value,
            portfolio_id=0,
            custom_flag='',
            seat_no='123',
            investor_id="001"
        )
        order_id = self.send_order(order)
        print(f"卖出订单已发送: ID={order_id}")
        return order_id

    def _cancel_last_order(self):
        if hasattr(self, '_last_order_id') and self._last_order_id > 0:
            print(f"发送撤单请求: ID={self._last_order_id}")
            order = FbCancelOrderEntity(
                instrument_id=self.instrument_id,
                order_id=self._last_order_id
            )
            result = self.cancel_order(order)
            print(f"撤单请求ID={self._last_order_id}已发送,结果：{result}")
        self._last_order_id = 0