from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List
from utils.flog import flogger


@dataclass
class StrategyInfo:
    """策略信息"""
    strategy_id: int
    strategy_name: str
    status: int  # 使用整数类型的状态值，对应 StrategyStateEnum 的值
    parameters: Dict[str, Any]
    node_id: int = 1  # 节点ID，用于标识不同的节点

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class StrategyParameter:
    """策略参数"""
    strategy_id: int
    param_name: str
    param_type: str
    param_value: Any
    node_id: int = 1  # 节点ID，用于标识不同的节点

    def to_dict(self) -> Dict[str, Any]:
        return {
            "strategy_id": self.strategy_id,
            "param_name": self.param_name,
            "param_type": self.param_type,
            "param_value": self.param_value,
            "node_id": self.node_id
        }


class StrategyManager:
    """策略管理器，负责管理策略信息供客户端获取"""

    def __init__(self, node_id: int = 1):
        self.node_id: int = node_id  # 节点ID，用于标识不同的节点
        self.strategies: Dict[int, StrategyInfo] = {}
        flogger.info("strategy manager service init", node_id=node_id)

    def add_strategy(self, strategy_id: int, strategy_name: str, status: int, parameters: Dict[str, Any]) -> None:
        """添加策略信息"""
        strategy_info = StrategyInfo(
            strategy_id=strategy_id,
            strategy_name=strategy_name,
            status=status,
            parameters=parameters,
            node_id=self.node_id
        )

        self.strategies[strategy_id] = strategy_info
        flogger.info("add strategy", node_id=self.node_id, strategy_id=strategy_id,
                     strategy_name=strategy_name, status=status, parameters=parameters)

    def update_strategy_status(self, strategy_id: int, status: int) -> bool:
        """更新策略状态"""
        if strategy_id not in self.strategies:
            flogger.warning("strategy not found",
                            node_id=self.node_id, strategy_id=strategy_id)
            return False

        self.strategies[strategy_id].status = status
        flogger.info("update strategy status", node_id=self.node_id,
                     strategy_id=strategy_id, status=status)
        return True

    def update_strategy_parameter(self, strategy_id: int, param_name: str, param_value: Any) -> bool:
        """更新策略参数"""
        if strategy_id not in self.strategies:
            flogger.warning("strategy not found",
                            node_id=self.node_id, strategy_id=strategy_id)
            return False

        self.strategies[strategy_id].parameters[param_name] = param_value
        flogger.info("update strategy params", node_id=self.node_id,
                     strategy_id=strategy_id, param_name=param_name, param_value=param_value)
        return True

    def get_strategy(self, strategy_id: int) -> Optional[StrategyInfo]:
        """获取策略信息"""
        flogger.info("get strategy info", strategy_id=strategy_id)
        return self.strategies.get(strategy_id)

    def get_all_strategies(self) -> List[StrategyInfo]:
        """获取所有策略信息"""
        flogger.info("get all strategies info")
        return list(self.strategies.values())

    def get_strategy_parameters(self, strategy_id: int) -> List[StrategyParameter]:
        """获取策略参数"""
        flogger.info("get strategy parameters", strategy_id=strategy_id)
        if strategy_id not in self.strategies:
            return []

        strategy = self.strategies[strategy_id]
        parameters = []

        for name, value in strategy.parameters.items():
            param = StrategyParameter(
                strategy_id=strategy_id,
                param_name=name,
                param_type=type(value).__name__,
                param_value=value,
                node_id=self.node_id
            )
            parameters.append(param)

        return parameters

    def delete_strategy(self, strategy_id: int) -> bool:
        """删除策略"""
        if strategy_id not in self.strategies:
            flogger.warning("strategy not found", strategy_id=strategy_id)
            return False

        del self.strategies[strategy_id]
        flogger.info("delete strategy", strategy_id=strategy_id)
        return True
