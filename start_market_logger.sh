#!/bin/bash

CURRENT_DIR=${pwd}

# 设置API基础URL
API_URL="http://localhost:5000/api"

# 加载策略文件
echo "加载策略文件..."
LOAD_RESPONSE=$(curl -s -X POST "$API_URL/strategy/load" \
  -H "Content-Type: application/json" \
  -d '{"file_path": "/dev2/pengyc/workspace/python_strategy/py_fb_strategy_new/strategies/demo_order_strategy.py"}')
echo $LOAD_RESPONSE

# 创建策略实例
echo "创建策略实例..."
CREATE_RESPONSE=$(curl -s -X POST "$API_URL/strategy/create" \
  -H "Content-Type: application/json" \
  -d '{
    "strategy_name": "DemoOrderStrategy",
    "params": {
      "instrument_id": "CFE_IF2412"
    }
  }')
echo $CREATE_RESPONSE

# 提取策略ID
STRATEGY_ID=$(echo $CREATE_RESPONSE | grep -o '"strategy_id":[0-9]*' | grep -o '[0-9]*')
echo "策略ID: $STRATEGY_ID"

# 保存策略ID到文件，供停止脚本使用
echo $STRATEGY_ID > .strategy_id

# 启动策略
echo "启动策略..."
START_RESPONSE=$(curl -s -X POST "$API_URL/strategy/start" \
  -H "Content-Type: application/json" \
  -d "{\"strategy_id\": $STRATEGY_ID}")
echo $START_RESPONSE

echo "策略已启动，查看日志: tail -f ./log/py_fb_strategy.log"
echo "要停止策略，请运行: ./stop_market_logger.sh"
