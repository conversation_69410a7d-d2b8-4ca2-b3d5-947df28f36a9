import os
import sys
import time
import signal
import argparse
from typing import Dict, Any
from pathlib import Path
from utils.flog import flogger
from common.event import EventEngine
from engine.strategy_engine import StrategyEngine
from web.http_server import HttpServer
from common.strategy_instance_manager import StrategyInstanceManager
from config.config import Config
from common.mdb import MemoryDatabase

class Main:
    def __init__(self, config_file: str):
        if config_file:
            if not os.path.isabs(config_file):
                config_file = os.path.abspath(config_file)
                print("config_file", config_file)
            
            if not os.path.exists(config_file):
                config_file = None
                print("config_file", config_file)
        self.config = Config.get_instance(config_file)
        self.event_engine = EventEngine()
        self.mdb = MemoryDatabase()
        # get node id
        strategy_engine_config = self.config.get_strategy_engine_config()
        flogger.info("strategy engine config", config = strategy_engine_config)
        node_id = strategy_engine_config.node_id
        flogger.info("node id retrieve", node_id = node_id)

        self.strategy_manager = StrategyInstanceManager.get_instance(node_id, self.mdb)
        self.http_server = HttpServer(self.event_engine, self.strategy_manager, self.config.get_http_server_config().to_dict())
        self.strategy_engine = StrategyEngine(
            self.event_engine,
            self.http_server,
            self.mdb,
            self.strategy_manager,
            self.config.get_strategy_engine_config()
        )
        self.running = False
        flogger.info("py_strategy_engine init complete")

    def start(self) -> None:
        flogger.info("starting py_strategy_engine")

        self.event_engine.start()
        flogger.info("event engine started")

        self.http_server.start()
        flogger.info("http server started")

        self.strategy_engine.start()
        flogger.info("strategy engine started")

        self.running = True
        flogger.info("py_strategy_engine started success", active_status = self.running)

    def stop(self) -> None:
        flogger.info("stopping py_strategy_engine")

        try:
            self.strategy_engine.stop()
            flogger.info("strategy engine stopped")
        except Exception as e:
            flogger.error("strategy engine stop failed", error = str(e))

        try:
            self.http_server.stop()
            flogger.info("http server stopped")
        except Exception as e:
            flogger.error("http server stop failed", error = str(e))

        try:
            self.event_engine.stop()
            flogger.info("event engine stopped")
        except Exception as e:
            flogger.error("event engine stop failed", error = str(e))

        self.running = False
        flogger.info("py_strategy_engine stopped success", active_status = self.running)

def signal_handler(signum: int, frame: Any) -> None:
    flogger.info("signal received", signum = signum)
    if main and main.running:
        main.stop()
    flogger.info("exit py_strategy_engine")
    sys.exit(0)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="策略引擎")
    parser.add_argument("--config", type=str, default="config/config.json", help="配置文件路径")
    args = parser.parse_args()

    # 注册信号处理函数
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建并启动主程序
    main = Main(args.config)
    main.start()

    # 主循环
    try:
        while main.running:
            time.sleep(1)
    except KeyboardInterrupt:
        main.stop()
