# py_fb_strategy 设计文档

## 1. 项目背景

py_fb_strategy 是 C++ 版本 fb_strategy 模块的 Python 实现。该项目旨在提供一个灵活、易于开发的策略编写环境，同时保持与原有系统的兼容性。

### 架构差异

与 C++ 版本相比，Python 版本有以下架构差异：

- **通信方式**：使用 HTTP 进行 Web 通信（策略管理），使用 Front API 进行核心模块通信（市场数据、订单等）
- **数据处理**：所有数据通过内存数据库（mdb）维护，无需额外的处理程序
- **请求处理**：采用同步 HTTP 请求处理模式，服务器等待处理完成后再发送响应
- **职责分离**：策略引擎仅管理策略生命周期，Front API 消息处理在单独的抽象类中

## 2. 系统架构

### 2.1 整体架构

整个策略交易系统由三层组成：

1. **客户端层**：用户界面，用于策略监控和管理
2. **中台层**：策略中台，管理多个策略引擎实例
3. **引擎层**：Python策略引擎实例，执行具体的策略逻辑

其中，py_fb_strategy 是引擎层的实现，采用模块化设计，主要包括以下组件：

- **策略引擎（StrategyEngine）**：负责策略的生命周期管理
- **策略流（StrategyStream）**：负责处理市场数据和事件分发
- **策略调用器（StrategyCaller）**：负责处理订单发送和撤销
- **事件引擎（EventEngine）**：负责系统内部事件的分发
- **内存数据库（MemoryDatabase）**：负责数据存储和管理
- **HTTP 服务器（HttpServer）**：提供 HTTP API 接口
- **WebSocket 发布器（WebSocketPublisher）**：负责实时数据推送

### 2.2 策略中台与引擎关系

策略中台是连接客户端和多个Python策略引擎的核心组件，主要职责包括：

1. **引擎管理**：
   - 启动、停止和监控多个Python策略引擎实例
   - 为每个引擎分配唯一的节点ID（node_id）
   - 维护引擎状态和健康检查

2. **请求路由**：
   - 接收来自客户端的请求
   - 根据请求内容确定目标引擎
   - 将请求转发到相应的Python策略引擎

3. **数据聚合**：
   - 接收来自所有Python策略引擎的WebSocket推送
   - 聚合数据并进行必要的处理
   - 将处理后的数据推送给客户端

4. **统一接口**：
   - 为客户端提供统一的API接口
   - 屏蔽底层引擎的实现细节
   - 提供跨引擎的查询和管理功能

```
                  +----------------+
                  |    客户端      |
                  +--------+-------+
                           |
                           | HTTP/WebSocket
                           |
                  +--------v-------+
                  |   策略中台     |
                  +--+------+-----+
                     |      |
          HTTP/WebSocket   HTTP/WebSocket
                     |      |
        +------------v-+  +-v------------+
        | Python策略引擎1 | | Python策略引擎2 |
        +--------------+  +--------------+
```

### 2.3 数据流向

#### 2.3.1 客户端到策略引擎的请求流

```
客户端 -> 策略中台 -> Python策略引擎
```

1. 客户端发送HTTP请求到策略中台
2. 策略中台解析请求，确定目标引擎
3. 策略中台将请求转发到相应的Python策略引擎
4. Python策略引擎处理请求并返回结果
5. 策略中台将结果返回给客户端

示例代码（策略中台转发请求）：
```python
# 策略中台转发创建策略请求
async def create_strategy(request: CreateStrategyRequest):
    # 确定目标引擎
    engine_id = determine_target_engine(request)

    # 构造转发请求
    forwarded_request = {
        "strategy_name": request.strategy_name,
        "params": request.params
    }

    # 获取引擎URL
    engine_url = f"http://{engine_hosts[engine_id]}:{engine_ports[engine_id]}/api/strategy/create"

    # 转发请求
    async with aiohttp.ClientSession() as session:
        async with session.post(engine_url, json=forwarded_request) as response:
            result = await response.json()

    # 返回结果
    return result
```

#### 2.3.2 策略引擎到客户端的推送流

```
Python策略引擎 -> 策略中台 -> 客户端
```

1. Python策略引擎通过WebSocket推送数据到策略中台
2. 策略中台接收并处理数据（如聚合、过滤）
3. 策略中台将处理后的数据推送给订阅的客户端

示例代码（策略中台处理推送）：
```python
# 策略中台处理WebSocket推送
async def handle_engine_message(websocket, engine_id):
    async for message in websocket:
        data = json.loads(message)

        # 添加引擎标识
        data["engine_id"] = engine_id

        # 根据主题分发
        topic = data["topic"]

        # 获取订阅该主题的客户端连接
        clients = topic_subscribers.get(topic, [])

        # 推送给客户端
        for client in clients:
            await client.send_json(data)
```

#### 2.3.3 市场数据流

```
市场数据 -> Front API -> 策略流 -> 策略实例
```

1. 市场数据通过Front API进入Python策略引擎
2. 策略流接收市场数据并通过事件引擎分发
3. 策略实例处理市场数据并执行交易逻辑

#### 2.3.4 交易指令流

```
策略实例 -> 策略调用器 -> Front API -> 交易系统
```

1. 策略实例生成交易指令
2. 策略调用器处理交易指令并通过Front API发送
3. 交易系统执行交易指令并返回结果

#### 2.3.5 引擎内部数据流示例

```python
# 1. Front API 接收市场数据
class FrontApi:
    def on_market_data(self, data):
        # 将市场数据转发到事件引擎
        event = Event(EVENT_MARKET_DATA, data)
        self.event_engine.put(event)

# 2. 策略流处理市场数据事件
class StrategyStream:
    def process_market_data(self, data):
        # 分发市场数据给相关策略
        for strategy in self.strategies.values():
            if strategy._status == StrategyStateEnum.STRATEGY_RUNNING_STAT.value:
                strategy.on_market_data(data)

# 3. 策略实例处理市场数据
class MyStrategy(StrategyApi):
    def on_market_data(self, tick):
        # 策略逻辑处理
        if self._should_trade(tick):
            self._send_order(tick)
```

#### 2.3.6 订单流向示例

```python
# 1. 策略发送订单
class MyStrategy(StrategyApi):
    def _send_order(self, tick):
        order = OrderEntity(
            instrument_id=tick.instrument_id,
            direction=DirectionEnum.BUY.value,
            offset=OffsetEnum.OPEN.value,
            price=tick.last_price,
            volume=1
        )
        order_id = self.send_order(order)

# 2. 策略调用器处理订单
class StrategyCaller:
    def send_order(self, strategy_id, order):
        # 生成内部订单ID
        order_id = self._generate_order_id()
        # 通过Front API发送订单
        self.front_api.send_order(order, order_id, strategy_id)
        return order_id

# 3. Front API接收订单状态更新
class FrontApi:
    def on_order(self, order):
        # 将订单状态转发到事件引擎
        event = Event(EVENT_ORDER, order)
        self.event_engine.put(event)
```

## 3. 数据模型

### 3.1 内存数据库

系统使用内存数据库存储和管理所有交易相关数据，包括：

- 市场数据（market_data）
- 期货合约（future_instrument）
- 期权合约（option_instrument）
- 订单（order）
- 成交（trade）
- 持仓（position）
- 策略实例（strategy_instance）

每个表格由表 ID 唯一标识，并定义了主键函数用于记录索引。

```python
# 表格枚举定义
class table_enum(Enum):
    """表格枚举"""
    market_data = 0X0015  # 原始市场行情
    future_instrument = 0X000A  # 期货合约信息
    option_instrument = 0X000B  # 期权合约
    order = 0X004E  # order指令流水
    trade = 0X004F  # 成交流水
    position = 0X0039  # 合约持仓信息
    strategy_instance = 0X005D  # 策略实例维护

# 表格初始化
def init_tables(mdb):
    """初始化表格"""
    mdb.register_table(
        table_enum.market_data,
        lambda x: x.instrument_id,
        MarketDataMsg
    )
    mdb.register_table(
        table_enum.future_instrument,
        lambda x: x.instrument_id,
        FutureInstrumentMsg
    )
    # 其他表格注册...
```

### 3.2 事件模型

系统使用事件驱动架构，主要事件类型包括：

- 市场数据事件（EVENT_MARKET_DATA）
- 订单事件（EVENT_ORDER）
- 成交事件（EVENT_TRADE）
- 策略管理事件（EVENT_LOAD_STRATEGY, EVENT_CREATE_STRATEGY 等）

事件通过事件引擎分发给相应的处理程序。

#### 3.2.1 事件引擎的作用与实现

事件引擎是系统的核心组件，负责事件的注册、分发和处理，是各个模块之间通信的桥梁。事件引擎的主要作用包括：

1. **解耦系统组件**：
   - 事件发送者和接收者之间不直接依赖，通过事件引擎进行通信
   - 各模块可以独立开发和测试，提高系统的可维护性

2. **异步处理**：
   - 事件放入队列后立即返回，不阻塞发送者
   - 事件处理在单独的线程中进行，提高系统的响应性

3. **集中管理**：
   - 所有事件在一个地方处理，便于监控和调试
   - 可以实现事件的优先级处理、过滤和转换

4. **扩展性支持**：
   - 新增事件类型和处理程序不需要修改现有代码
   - 支持动态注册和注销事件处理程序

```python
# 事件引擎实现
class EventEngine:
    """事件引擎，负责事件的分发和处理"""
    def __init__(self):
        # 事件队列
        self.queue = Queue()
        # 事件处理器字典，key为事件类型，value为处理函数列表
        self.handlers = defaultdict(list)
        # 活动标志
        self.active = False
        # 处理线程
        self.thread = None

    def start(self):
        """启动事件引擎"""
        if self.active:
            return

        self.active = True
        self.thread = threading.Thread(target=self._run, name="EventEngine")
        self.thread.daemon = True
        self.thread.start()

    def stop(self):
        """停止事件引擎"""
        self.active = False
        if self.thread and self.thread.is_alive():
            self.thread.join()

    def _run(self):
        """事件处理循环"""
        while self.active:
            try:
                # 获取事件，最多等待1秒
                event = self.queue.get(block=True, timeout=1)
                self._process(event)
            except Empty:
                pass

    def _process(self, event):
        """处理事件"""
        # 获取事件类型
        event_type = event.type

        # 获取该事件类型的处理函数列表
        handlers = self.handlers.get(event_type, [])

        # 依次调用处理函数
        for handler in handlers:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"事件处理异常: {e}")

    def register(self, event_type, handler):
        """注册事件处理函数"""
        handler_list = self.handlers[event_type]
        if handler not in handler_list:
            handler_list.append(handler)

    def unregister(self, event_type, handler):
        """注销事件处理函数"""
        handler_list = self.handlers.get(event_type, [])
        if handler in handler_list:
            handler_list.remove(handler)
        if not handler_list:
            self.handlers.pop(event_type, None)

    def put(self, event):
        """将事件放入队列"""
        self.queue.put(event)
```

#### 3.2.2 事件流转示例

以下是一个完整的事件流转示例，展示了从市场数据接收到策略处理的整个过程：

1. **Front API 接收市场数据**：
   ```python
   def on_market_data(self, data):
       # 创建市场数据事件
       event = Event(EVENT_MARKET_DATA, data)
       # 放入事件引擎
       self.event_engine.put(event)
   ```

2. **策略流注册事件处理函数**：
   ```python
   def __init__(self, event_engine):
       self.event_engine = event_engine
       # 注册市场数据事件处理函数
       self.event_engine.register(EVENT_MARKET_DATA, self.process_market_data)
   ```

3. **事件引擎分发事件**：
   ```python
   def _process(self, event):
       # 获取事件类型
       event_type = event.type
       # 获取处理函数列表
       handlers = self.handlers.get(event_type, [])
       # 调用处理函数
       for handler in handlers:
           handler(event)
   ```

4. **策略流处理事件**：
   ```python
   def process_market_data(self, event):
       # 获取市场数据
       data = event.data
       # 分发给策略
       for strategy in self.strategies.values():
           if strategy._status == StrategyStateEnum.STRATEGY_RUNNING_STAT.value:
               strategy.on_market_data(data)
   ```

通过这种事件驱动的方式，系统可以高效地处理各种类型的事件，并将它们分发给相应的处理程序，实现模块间的解耦和异步处理。

## 4. 线程模型

系统采用多线程架构，主要包括：

- **主线程**：负责初始化和信号处理
- **事件引擎线程**：负责事件处理
- **HTTP/WebSocket 服务器线程**：负责处理 HTTP 请求和 WebSocket 连接

线程间通过事件和消息队列进行通信，确保数据的一致性和线程安全。

### 4.1 线程模型示例

```python
# 主线程初始化和启动
class Main:
    def __init__(self):
        self.event_engine = EventEngine()
        self.publisher = WebSocketPublisher(node_id=node_id)
        self.http_server = HttpServer(self.event_engine, self.publisher, http_config)
        self.strategy_engine = StrategyEngine(self.event_engine, self.publisher, engine_config)
        self.running = False

    def start(self):
        # 启动事件引擎
        self.event_engine.start()
        # 启动HTTP服务器
        self.http_server.start()
        # 启动策略引擎
        self.strategy_engine.start()
        self.running = True

# 事件引擎线程
class EventEngine:
    def start(self):
        self.thread = threading.Thread(target=self._run, name="EventEngine")
        self.thread.daemon = True
        self.thread.start()

    def _run(self):
        while self.active:
            try:
                event = self.queue.get(block=True, timeout=1)
                self._process(event)
            except Empty:
                pass

# HTTP服务器线程
class HttpServer:
    def start(self):
        self.thread = threading.Thread(target=self._run, name="HttpServer")
        self.thread.daemon = True
        self.thread.start()

    def _run(self):
        uvicorn.run(
            self.app,
            host=self.host,
            port=self.port,
            timeout_keep_alive=0  # 禁用超时
        )
```

### 4.2 线程间通信

```python
# 通过事件引擎进行线程间通信
def handle_http_request(request_id, event_type, data):
    # HTTP线程接收请求
    event = Event(event_type, {
        "request_id": request_id,
        **data
    })
    # 将事件放入事件引擎队列
    event_engine.put(event)
    # 等待处理结果
    future = response_manager.register_request(request_id, event_type)
    response = await asyncio.wait_for(future, timeout=30.0)
    return response

# 事件引擎线程处理事件
def handle_event(event):
    # 处理事件
    result = process_event(event)
    # 发送处理结果
    request_id = event.data.get("request_id")
    response_manager.send_response(request_id, result)
```

## 5. 接口设计

### 5.1 HTTP API

系统提供以下 HTTP API 接口：

- `POST /api/strategy/load`：加载策略
- `POST /api/strategy/create`：创建策略实例
- `POST /api/strategy/start`：启动策略
- `POST /api/strategy/stop`：停止策略
- `POST /api/strategy/delete`：删除策略
- `POST /api/strategy/parameter_update`：更新策略参数

所有 HTTP 请求采用同步处理模式，服务器等待处理完成后再发送响应。

#### 5.1.0 HTTP 数据流与作用

HTTP 接口主要用于策略管理，处理来自用户界面或管理系统的请求。HTTP 数据流如下：

**接收数据**：
- 策略加载请求：`{file_path: "path/to/strategy.py"}`
- 策略创建请求：`{strategy_name: "MyStrategy", params: {symbol: "rb2101", volume: 1}}`
- 策略启动请求：`{strategy_id: 12345}`
- 策略停止请求：`{strategy_id: 12345}`
- 策略删除请求：`{strategy_id: 12345}`
- 策略参数更新请求：`{strategy_id: 12345, param_name: "volume", param_value: 2}`

**响应数据**：
- 成功响应：`{success: true, message: "操作成功", data: {...}, request_id: 12345}`
- 失败响应：`{success: false, message: "错误信息", data: null, request_id: 12345}`

HTTP 接口的主要作用是：
1. 提供策略生命周期管理（加载、创建、启动、停止、删除）
2. 支持策略参数动态调整
3. 提供同步响应模式，确保操作完成后才返回结果
4. 与前端界面或管理系统集成，提供用户交互入口

#### 5.1.1 创建策略实例示例

```python
# HTTP API路由定义
@app.post("/api/strategy/create", response_model=ApiResponseModel)
async def create_strategy(request: CreateStrategyRequest):
    # 生成请求ID
    request_id = response_manager.generate_request_id()

    # 注册请求并获取Future对象
    future = response_manager.register_request(request_id, "/api/strategy/create")

    # 创建事件并放入事件引擎
    event = Event(EVENT_CREATE_STRATEGY, {
        "strategy_name": request.strategy_name,
        "params": request.params or {},
        "request_id": request_id
    })
    event_engine.put(event)

    # 等待处理完成并返回响应
    return await wait_for_response(future, request_id)

# 策略引擎处理创建策略事件
def handle_create_strategy(self, event):
    strategy_name = event.data["strategy_name"]
    params = event.data["params"]
    request_id = event.data["request_id"]

    # 创建策略实例
    strategy_id = self.strategy_factory.create_strategy(strategy_name, params)

    if strategy_id > 0:
        # 创建成功
        self.response_manager.send_response_sync(
            request_id,
            success=True,
            message="策略创建成功",
            data={"strategy_id": strategy_id}
        )
    else:
        # 创建失败
        self.response_manager.send_response_sync(
            request_id,
            success=False,
            message="策略创建失败"
        )
```

### 5.2 WebSocket 接口

系统提供 WebSocket 接口用于实时数据推送，包括：

- 策略状态更新
- 策略参数更新
- 订单和成交信息

客户端可以订阅特定主题的消息，系统会将相关数据推送给订阅者。

#### 5.2.1 WebSocket 数据流与作用

WebSocket 接口主要用于实时数据推送，将系统内部状态变化及时通知给客户端。WebSocket 数据流如下：

**推送数据**：
- 策略信息：`{topic: "strategy_info", data: {strategy_id: 12345, strategy_name: "MyStrategy", status: 1, parameters: {...}}, node_id: 1, timestamp: 1623456789}`
- 策略状态更新：`{topic: "strategy_status", data: {strategy_id: 12345, status: 2}, node_id: 1, timestamp: 1623456789}`
- 策略参数更新：`{topic: "strategy_parameter", data: {strategy_id: 12345, param_name: "volume", param_value: 2}, node_id: 1, timestamp: 1623456789}`
- 订单更新：`{topic: "order", data: {order_id: 67890, strategy_id: 12345, instrument_id: "rb2101", status: 3, ...}, node_id: 1, timestamp: 1623456789}`
- 成交更新：`{topic: "trade", data: {trade_id: 54321, order_id: 67890, strategy_id: 12345, ...}, node_id: 1, timestamp: 1623456789}`

**订阅请求**：
- 全部主题：`{topics: ["all"]}`
- 特定主题：`{topics: ["strategy_status", "order"]}`
- 特定策略：`{topics: ["strategy_12345"]}`

WebSocket 接口的主要作用是：
1. 提供实时数据推送，确保客户端能够及时获取系统状态变化
2. 支持按主题订阅，减少不必要的数据传输
3. 维持长连接，避免频繁的连接建立和断开
4. 支持多客户端同时连接，实现广播和点对点通信

```python
# WebSocket 推送示例
class WebSocketPublisher:
    async def publish(self, topic: str, data: Any) -> None:
        """向指定主题的所有连接发送消息"""
        # 准备消息
        message = {
            "topic": topic,
            "data": data,
            "node_id": self.node_id,
            "timestamp": time.time()
        }

        # 序列化消息
        message_json = json.dumps(message)

        # 获取订阅该主题的连接
        connections = self.active_connections.get(topic, [])
        connections.extend(self.active_connections.get("all", []))

        # 发送消息给所有订阅者
        for websocket in connections:
            try:
                await websocket.send_text(message_json)
            except Exception as e:
                logger.error(f"发送消息异常: {e}")
                # 移除失效的连接
                await self.disconnect(websocket)
```

### 5.3 策略 API

策略 API 是策略开发者用于编写策略的接口，主要包括：

- **生命周期方法**：`on_init`, `on_start`, `on_stop`
- **事件回调方法**：`on_market_data`, `on_order`, `on_trade`
- **交易方法**：`send_order`, `cancel_order`
- **数据访问方法**：`get_market_data`, `get_future_instrument` 等

策略开发者通过继承 `StrategyApi` 类并实现相应方法来开发策略。

#### 5.3.1 策略开发示例

```python
from py_strategy_api.strategy_api import StrategyApi
from py_strategy_api.models import OrderEntity, MarketDataMsg
from py_strategy_api.enum import DirectionEnum, OffsetEnum, OrderTypeEnum

class SimpleMAStrategy(StrategyApi):
    strategy_verison = "1.0.0"
    parameters = ["symbol", "volume", "fast_period", "slow_period"]

    def __init__(self, engine):
        super().__init__(engine)
        self.symbol = "rb2101"  # 交易品种
        self.volume = 1  # 交易数量
        self.fast_period = 5  # 快速均线周期
        self.slow_period = 20  # 慢速均线周期

        self.prices = []  # 价格历史
        self.positions = {}  # 持仓记录

    def on_init(self):
        """策略初始化"""
        print(f"策略初始化: {self.get_name()}")

    def on_start(self):
        """策略启动"""
        print(f"策略启动: {self.get_name()}")
        # 获取合约信息
        instrument = self.get_future_instrument(self.symbol)
        if instrument:
            print(f"合约信息: {instrument.instrument_id}, 价格精度: {instrument.price_tick}")

    def on_market_data(self, tick: MarketDataMsg):
        """市场数据回调"""
        if tick.instrument_id != self.symbol:
            return

        # 记录价格
        self.prices.append(tick.last_price)
        if len(self.prices) > self.slow_period:
            self.prices.pop(0)

        # 计算均线
        if len(self.prices) >= self.slow_period:
            fast_ma = sum(self.prices[-self.fast_period:]) / self.fast_period
            slow_ma = sum(self.prices) / self.slow_period

            # 交易信号
            position = self.get_position(self.symbol, DirectionEnum.BUY.value)
            position_volume = position.volume if position else 0

            # 金叉做多
            if fast_ma > slow_ma and position_volume == 0:
                self.open_long(tick)

            # 死叉平多
            elif fast_ma < slow_ma and position_volume > 0:
                self.close_long(tick)

    def open_long(self, tick):
        """开多"""
        order = OrderEntity(
            instrument_id=self.symbol,
            exchange_id=tick.exchange_id,
            direction=DirectionEnum.BUY.value,
            offset=OffsetEnum.OPEN.value,
            price=tick.last_price,
            volume=self.volume,
            order_type=OrderTypeEnum.LIMIT.value
        )
        order_id = self.send_order(order)
        print(f"发送开多订单: ID={order_id}, 价格={tick.last_price}")
```

## 6. 配置管理

系统使用 JSON 格式的配置文件，主要配置项包括：

- **节点 ID**：用于标识不同的节点
- **HTTP 服务器配置**：主机、端口等
- **Front API 配置**：地址、日志级别等

配置文件通过 `Config` 类加载和管理，确保配置的一致性和可访问性。

## 7. 日志管理

系统使用统一的日志管理，记录系统运行状态和错误信息。日志分为不同级别：

- DEBUG：调试信息
- INFO：一般信息
- WARNING：警告信息
- ERROR：错误信息
- CRITICAL：严重错误信息

## 8. 错误处理

系统采用多层次的错误处理机制：

- **异常捕获**：捕获并记录策略执行过程中的异常
- **超时处理**：处理请求超时情况
- **错误响应**：返回标准化的错误响应

## 9. 扩展性设计

系统设计考虑了扩展性：

- **模块化设计**：各组件职责明确，便于扩展
- **事件驱动**：通过添加新的事件类型和处理程序扩展功能
- **表格注册**：通过注册新的表格扩展数据模型
- **策略接口**：提供统一的策略接口，便于开发新策略

## 10. 部署与运维

系统部署简单，主要步骤：

- 安装依赖
- 配置系统参数
- 启动服务

运维方面，系统提供：

- 健康检查接口
- 日志监控
- 状态查询

## 11. 策略中台详细设计

策略中台作为连接客户端和多个Python策略引擎的核心组件，需要更详细的设计说明。

### 11.1 中台架构

策略中台采用微服务架构，主要包括以下组件：

1. **API网关**：
   - 提供统一的API入口
   - 处理认证和授权
   - 实现请求路由和负载均衡

2. **引擎管理服务**：
   - 管理Python策略引擎的生命周期
   - 监控引擎健康状态
   - 处理引擎的启动、停止和重启

3. **数据聚合服务**：
   - 接收和处理来自所有引擎的数据
   - 实现数据的过滤、转换和聚合
   - 提供数据缓存和历史查询

4. **WebSocket服务**：
   - 维护与客户端的WebSocket连接
   - 处理客户端的订阅请求
   - 推送实时数据到客户端

5. **配置管理服务**：
   - 管理系统配置和策略参数
   - 提供配置的版本控制和回滚
   - 支持配置的动态更新

### 11.2 中台与引擎的通信

#### 11.2.1 HTTP通信

中台通过HTTP与Python策略引擎进行通信，主要用于：

1. **引擎管理**：
   - 启动和停止策略
   - 更新策略参数
   - 查询策略状态

2. **数据查询**：
   - 获取策略列表
   - 查询订单和成交信息
   - 获取持仓和资金信息

示例代码（中台调用引擎API）：
```python
async def start_strategy(engine_id, strategy_id):
    """启动策略"""
    engine_url = get_engine_url(engine_id)
    url = f"{engine_url}/api/strategy/start"

    async with aiohttp.ClientSession() as session:
        async with session.post(url, json={"strategy_id": strategy_id}) as response:
            if response.status == 200:
                return await response.json()
            else:
                raise Exception(f"启动策略失败: {await response.text()}")
```

#### 11.2.2 WebSocket通信

中台通过WebSocket接收Python策略引擎的实时数据推送：

1. **连接管理**：
   - 为每个引擎维护一个WebSocket连接
   - 处理连接断开和重连
   - 实现心跳检测

2. **数据处理**：
   - 接收引擎推送的数据
   - 添加引擎标识和时间戳
   - 根据订阅关系转发给客户端

示例代码（中台处理引擎推送）：
```python
async def connect_to_engine(engine_id):
    """连接到引擎的WebSocket"""
    engine_url = get_engine_ws_url(engine_id)

    while True:
        try:
            async with websockets.connect(engine_url) as websocket:
                # 发送订阅请求
                await websocket.send(json.dumps({
                    "topics": ["all"]
                }))

                # 处理接收到的消息
                async for message in websocket:
                    await process_engine_message(engine_id, message)
        except Exception as e:
            logger.error(f"引擎{engine_id}的WebSocket连接异常: {e}")
            # 等待一段时间后重连
            await asyncio.sleep(5)
```

### 11.3 中台与客户端的通信

#### 11.3.1 HTTP API

中台为客户端提供统一的HTTP API：

1. **策略管理**：
   - 创建、启动、停止和删除策略
   - 更新策略参数
   - 查询策略状态

2. **数据查询**：
   - 获取所有引擎的策略列表
   - 查询订单和成交历史
   - 获取持仓和资金信息

3. **系统管理**：
   - 引擎的启动和停止
   - 系统配置管理
   - 用户认证和授权

#### 11.3.2 WebSocket推送

中台为客户端提供WebSocket接口，用于实时数据推送：

1. **订阅机制**：
   - 支持按主题订阅
   - 支持按引擎ID订阅
   - 支持按策略ID订阅

2. **推送内容**：
   - 策略状态更新
   - 订单和成交信息
   - 持仓和资金变化
   - 系统事件和告警

示例代码（中台推送给客户端）：
```python
async def push_to_client(client_id, topic, data):
    """推送数据给客户端"""
    if client_id in active_clients:
        websocket = active_clients[client_id]
        message = {
            "topic": topic,
            "data": data,
            "timestamp": time.time()
        }
        try:
            await websocket.send_json(message)
        except Exception as e:
            logger.error(f"推送数据给客户端{client_id}失败: {e}")
            # 移除失效的连接
            await disconnect_client(client_id)
```

### 11.4 节点ID管理

中台负责为每个Python策略引擎分配唯一的节点ID（node_id）：

1. **分配机制**：
   - 静态配置：在配置文件中为每个引擎指定固定的节点ID
   - 动态分配：中台在引擎启动时动态分配节点ID

2. **ID用途**：
   - 标识不同的策略引擎实例
   - 在数据推送中包含节点ID，便于客户端识别数据来源
   - 用于生成全局唯一的订单ID和策略ID

3. **冲突处理**：
   - 检测节点ID冲突
   - 防止重复分配
   - 处理引擎重启时的ID恢复

## 12. 未来规划

系统未来可能的扩展方向：

- 支持更多类型的策略
- 增强数据分析能力
- 优化性能和可靠性
- 增加更多的监控和管理功能
- 完善策略中台功能，提供更强大的跨引擎管理能力
- 实现策略的热部署和动态加载
- 支持分布式部署和容器化
